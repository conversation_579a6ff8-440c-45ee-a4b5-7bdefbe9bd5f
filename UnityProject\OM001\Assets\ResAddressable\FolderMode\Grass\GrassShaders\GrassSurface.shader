Shader "Custom/GrassComputeSurface_URP"
{
	Properties
	{
		[Toggle(BLEND)] _BlendFloor("Blend with floor", Float) = 0
		_Fade("Top Fade Offset", Range(-1,10)) = 1
		_Stretch("Top Fade Stretch", Range(-1,10)) = 1
		_AmbientAdjustmentColor("Ambient Adjustment Color", Color) = (0.5,0.5,0.5,1)
		_Metallic("Metallic", Range(0,1)) = 1
		_Smoothness("Smoothness", Range(0,1)) = 1
		_Edge("Edge", Range(0,1)) = 1
		_TopTint("Top Tint", Color) = (1,1,1,1)
		_BottomTint("Bottom Tint", Color) = (1,1,1,1)
	}
	SubShader
	{
		Tags {
			"RenderType" = "Opaque"
			"RenderPipeline" = "UniversalPipeline"
			"Queue" = "Geometry"
		}
		LOD 200
		Cull Off

		Pass
		{
			Name "ForwardLit"
			Tags { "LightMode" = "UniversalForward" }

			HLSLPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma target 4.5
			#pragma shader_feature_local BLEND
			#pragma multi_compile _ _MAIN_LIGHT_SHADOWS _MAIN_LIGHT_SHADOWS_CASCADE _MAIN_LIGHT_SHADOWS_SCREEN
			#pragma multi_compile _ _ADDITIONAL_LIGHTS_VERTEX _ADDITIONAL_LIGHTS
			#pragma multi_compile_fragment _ _ADDITIONAL_LIGHT_SHADOWS
			#pragma multi_compile_fragment _ _SHADOWS_SOFT
			#pragma multi_compile _ LIGHTMAP_SHADOW_MIXING
			#pragma multi_compile _ SHADOWS_SHADOWMASK
			#pragma multi_compile_fragment _ _SCREEN_SPACE_OCCLUSION
			#pragma multi_compile_instancing

			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Shadows.hlsl"

			// Structures
			struct DrawVertex
			{
				float3 positionWS; // The position in world space
				float2 uv;
			};

			// A triangle on the generated mesh
			struct DrawTriangle
			{
				float3 normalOS;
				float3 diffuseColor;
				float4 extraBuffer;
				DrawVertex vertices[3]; // The three points on the triangle
			};

			#ifdef SHADER_API_D3D11
				StructuredBuffer<DrawTriangle> _DrawTriangles;
			#endif
			#ifdef SHADER_API_METAL
				StructuredBuffer<DrawTriangle> _DrawTriangles;
			#endif

			// Properties
			CBUFFER_START(UnityPerMaterial)
				float4 _TopTint;
				float4 _BottomTint;
				float _Fade, _Stretch;
				float _Metallic;
				float _Smoothness;
				float _Edge;
				float4 _AmbientAdjustmentColor;
			CBUFFER_END

			float _OrthographicCamSizeTerrain;
			float3 _OrthographicCamPosTerrain;
			TEXTURE2D(_TerrainDiffuse);
			SAMPLER(sampler_TerrainDiffuse);

			struct Attributes
			{
				float4 positionOS : POSITION;
				float3 normalOS : NORMAL;
				float4 tangentOS : TANGENT;
				float2 texcoord : TEXCOORD0;
				uint vertexID : SV_VertexID;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct Varyings
			{
				float4 positionCS : SV_POSITION;
				float3 positionWS : TEXCOORD0;
				float3 normalWS : TEXCOORD1;
				float2 uv : TEXCOORD2;
				float3 diffuseColor : TEXCOORD3;
				float4 extraBuffer : TEXCOORD4;
				float2 texcoord : TEXCOORD5;
				float4 shadowCoord : TEXCOORD6;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			Varyings vert(Attributes input)
			{
				Varyings output = (Varyings)0;

				UNITY_SETUP_INSTANCE_ID(input);
				UNITY_TRANSFER_INSTANCE_ID(input, output);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

				#ifdef SHADER_API_D3D11
					// Get the vertex from the buffer
					DrawTriangle tri = _DrawTriangles[input.vertexID / 3];
					DrawVertex vertexData = tri.vertices[input.vertexID % 3];

					output.positionWS = vertexData.positionWS;
					output.positionCS = TransformWorldToHClip(vertexData.positionWS);
					output.normalWS = TransformObjectToWorldNormal(tri.normalOS);
					output.uv = vertexData.uv;
					output.texcoord = vertexData.uv;
					output.extraBuffer = tri.extraBuffer;
					if(tri.extraBuffer.x == -1){
						output.extraBuffer.x = 9999;
					}
					output.diffuseColor = tri.diffuseColor;
				#endif
				#ifdef SHADER_API_METAL
					// Get the vertex from the buffer
					DrawTriangle tri = _DrawTriangles[input.vertexID / 3];
					DrawVertex vertexData = tri.vertices[input.vertexID % 3];

					output.positionWS = vertexData.positionWS;
					output.positionCS = TransformWorldToHClip(vertexData.positionWS);
					output.normalWS = TransformObjectToWorldNormal(tri.normalOS);
					output.uv = vertexData.uv;
					output.texcoord = vertexData.uv;
					output.extraBuffer = tri.extraBuffer;
					if(tri.extraBuffer.x == -1){
						output.extraBuffer.x = 9999;
					}
					output.diffuseColor = tri.diffuseColor;
				#endif

				// Calculate shadow coordinates
				output.shadowCoord = TransformWorldToShadowCoord(output.positionWS);

				return output;
			}

			half4 frag(Varyings input) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(input);
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);

				// Height clipping
				float cutoffTop = step(input.positionWS.y, input.extraBuffer.x);
				clip(cutoffTop - 0.01);

				// Rendertexture UV for terrain blending
				float2 terrainUV = input.positionWS.xz - _OrthographicCamPosTerrain.xz;
				terrainUV = terrainUV / (_OrthographicCamSizeTerrain * 2);
				terrainUV += 0.5;

				// Fade over the length of the grass
				float verticalFade = saturate((input.texcoord.y + _Fade) * _Stretch);

				// Colors from the tool with tinting from the grass script
				float4 baseColor = lerp(_BottomTint, _TopTint * _AmbientAdjustmentColor, verticalFade) * float4(input.diffuseColor, 1);

				// Get the floor map
				float4 terrainForBlending = SAMPLE_TEXTURE2D(_TerrainDiffuse, sampler_TerrainDiffuse, terrainUV);

				float4 finalColor = baseColor;
				#if BLEND
					finalColor = lerp(terrainForBlending, terrainForBlending + (_TopTint * float4(input.diffuseColor, 1) * _AmbientAdjustmentColor), verticalFade);
				#endif

				float outside = saturate(abs(input.texcoord.x - 0.5) + _Edge) * input.texcoord.y;

				// Setup lighting
				InputData lightingInput = (InputData)0;
				lightingInput.positionWS = input.positionWS;
				lightingInput.normalWS = normalize(input.normalWS);
				lightingInput.viewDirectionWS = GetWorldSpaceNormalizeViewDir(input.positionWS);
				lightingInput.shadowCoord = input.shadowCoord;
				lightingInput.fogCoord = 0;
				lightingInput.vertexLighting = half3(0, 0, 0);
				lightingInput.bakedGI = half3(0, 0, 0);
				lightingInput.normalizedScreenSpaceUV = GetNormalizedScreenSpaceUV(input.positionCS);
				lightingInput.shadowMask = half4(1, 1, 1, 1);

				// Setup surface data
				SurfaceData surfaceData = (SurfaceData)0;
				surfaceData.albedo = finalColor.rgb;
				surfaceData.metallic = _Metallic * outside;
				surfaceData.specular = half3(0, 0, 0);
				surfaceData.smoothness = _Smoothness * outside;
				surfaceData.normalTS = half3(0, 0, 1);
				surfaceData.emission = half3(0, 0, 0);
				surfaceData.occlusion = 1;
				surfaceData.alpha = 1;
				surfaceData.clearCoatMask = 0;
				surfaceData.clearCoatSmoothness = 0;

				// Calculate lighting
				half4 color = UniversalFragmentPBR(lightingInput, surfaceData);

				return color;
			}
			ENDHLSL
		}

		Pass
		{
			Name "ShadowCaster"
			Tags { "LightMode" = "ShadowCaster" }

			ZWrite On
			ZTest LEqual
			ColorMask 0
			Cull Off

			HLSLPROGRAM
			#pragma vertex ShadowPassVertex
			#pragma fragment ShadowPassFragment
			#pragma target 4.5
			#pragma multi_compile_instancing

			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Shadows.hlsl"

			struct ShadowAttributes
			{
				float4 positionOS : POSITION;
				float3 normalOS : NORMAL;
				uint vertexID : SV_VertexID;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct ShadowVaryings
			{
				float4 positionCS : SV_POSITION;
				float3 positionWS : TEXCOORD0;
				float4 extraBuffer : TEXCOORD1;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			ShadowVaryings ShadowPassVertex(ShadowAttributes input)
			{
				ShadowVaryings output = (ShadowVaryings)0;
				UNITY_SETUP_INSTANCE_ID(input);
				UNITY_TRANSFER_INSTANCE_ID(input, output);

				#ifdef SHADER_API_D3D11
					DrawTriangle tri = _DrawTriangles[input.vertexID / 3];
					DrawVertex vertexData = tri.vertices[input.vertexID % 3];

					output.positionWS = vertexData.positionWS;
					output.extraBuffer = tri.extraBuffer;
					if(tri.extraBuffer.x == -1){
						output.extraBuffer.x = 9999;
					}
				#endif
				#ifdef SHADER_API_METAL
					DrawTriangle tri = _DrawTriangles[input.vertexID / 3];
					DrawVertex vertexData = tri.vertices[input.vertexID % 3];

					output.positionWS = vertexData.positionWS;
					output.extraBuffer = tri.extraBuffer;
					if(tri.extraBuffer.x == -1){
						output.extraBuffer.x = 9999;
					}
				#endif

				output.positionCS = TransformWorldToHClip(output.positionWS);
				return output;
			}

			half4 ShadowPassFragment(ShadowVaryings input) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(input);

				// Height clipping
				float cutoffTop = step(input.positionWS.y, input.extraBuffer.x);
				clip(cutoffTop - 0.01);

				return 0;
			}
			ENDHLSL
		}
	}
	Fallback "Hidden/Universal Render Pipeline/FallbackError"
}