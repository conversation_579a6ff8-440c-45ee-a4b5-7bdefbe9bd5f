%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12004, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PixelRect:
    serializedVersion: 2
    x: 0
    y: 43
    width: 2560
    height: 989
  m_ShowMode: 4
  m_Title: Console
  m_RootView: {fileID: 2}
  m_MinSize: {x: 875, y: 300}
  m_MaxSize: {x: 10000, y: 10000}
  m_Maximized: 1
--- !u!114 &2
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12008, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 3}
  - {fileID: 5}
  - {fileID: 4}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 2560
    height: 989
  m_MinSize: {x: 875, y: 300}
  m_MaxSize: {x: 10000, y: 10000}
  m_UseTopView: 1
  m_TopViewHeight: 36
  m_UseBottomView: 1
  m_BottomViewHeight: 20
--- !u!114 &3
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12011, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 2560
    height: 36
  m_MinSize: {x: 0, y: 0}
  m_MaxSize: {x: 0, y: 0}
  m_LastLoadedLayoutName: 
--- !u!114 &4
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12042, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 969
    width: 2560
    height: 20
  m_MinSize: {x: 0, y: 0}
  m_MaxSize: {x: 0, y: 0}
--- !u!114 &5
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 6}
  - {fileID: 9}
  - {fileID: 14}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 36
    width: 2560
    height: 933
  m_MinSize: {x: 400, y: 100}
  m_MaxSize: {x: 32384, y: 16192}
  vertical: 0
  controlID: 25
  draggingID: 0
--- !u!114 &6
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 7}
  - {fileID: 8}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1169
    height: 933
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 8096, y: 16192}
  vertical: 1
  controlID: 26
  draggingID: 0
--- !u!114 &7
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: SceneView
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1169
    height: 685
  m_MinSize: {x: 201, y: 226}
  m_MaxSize: {x: 4001, y: 4026}
  m_ActualView: {fileID: 18}
  m_Panes:
  - {fileID: 18}
  - {fileID: 19}
  - {fileID: 20}
  - {fileID: 15}
  m_Selected: 0
  m_LastSelected: 1
--- !u!114 &8
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: GameView
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 685
    width: 1169
    height: 248
  m_MinSize: {x: 201, y: 226}
  m_MaxSize: {x: 4001, y: 4026}
  m_ActualView: {fileID: 17}
  m_Panes:
  - {fileID: 17}
  - {fileID: 21}
  - {fileID: 22}
  - {fileID: 16}
  m_Selected: 0
  m_LastSelected: 2
--- !u!114 &9
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 10}
  - {fileID: 13}
  m_Position:
    serializedVersion: 2
    x: 1169
    y: 0
    width: 769
    height: 933
  m_MinSize: {x: 200, y: 100}
  m_MaxSize: {x: 16192, y: 16192}
  vertical: 0
  controlID: 132
  draggingID: 0
--- !u!114 &10
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 11}
  - {fileID: 12}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 374
    height: 933
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 8096, y: 16192}
  vertical: 1
  controlID: 107
  draggingID: 0
--- !u!114 &11
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: SceneHierarchyWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 374
    height: 492
  m_MinSize: {x: 202, y: 226}
  m_MaxSize: {x: 4002, y: 4026}
  m_ActualView: {fileID: 23}
  m_Panes:
  - {fileID: 23}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &12
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: ConsoleWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 492
    width: 374
    height: 441
  m_MinSize: {x: 102, y: 126}
  m_MaxSize: {x: 4002, y: 4026}
  m_ActualView: {fileID: 24}
  m_Panes:
  - {fileID: 24}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &13
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: ProjectBrowser
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 374
    y: 0
    width: 395
    height: 933
  m_MinSize: {x: 232, y: 276}
  m_MaxSize: {x: 10002, y: 10026}
  m_ActualView: {fileID: 25}
  m_Panes:
  - {fileID: 25}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &14
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: InspectorWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 1938
    y: 0
    width: 622
    height: 933
  m_MinSize: {x: 276, y: 76}
  m_MaxSize: {x: 4001, y: 4026}
  m_ActualView: {fileID: 26}
  m_Panes:
  - {fileID: 26}
  - {fileID: 27}
  m_Selected: 0
  m_LastSelected: 1
--- !u!114 &15
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 924ffcbe75518854f97b48776d0f1939, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: ShaderGraph GrassUnlitBasic
    m_Image: {fileID: 2800000, guid: 7129268cf102b2f45809905bcb27ce8b, type: 3}
    m_Tooltip: 
    m_TextWithWhitespace: "ShaderGraph GrassUnlitBasic\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 79.200005
    width: 761.4
    height: 609.2
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_Selected: ba558017391f65e4295aec8be8c738a1
  m_GraphObject: {fileID: 0}
  m_LastSerializedFileContents: "{\n    \"m_SGVersion\": 3,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GraphData\",\n   
    \"m_ObjectId\": \"27fbb28adae64e2884dbd7feb127af7e\",\n    \"m_Properties\":
    [\n        {\n            \"m_Id\": \"163bfa18e4ab4e75b93815703c941ea0\"\n       
    },\n        {\n            \"m_Id\": \"217ecc1b3f414f6298c540a27a883e11\"\n       
    },\n        {\n            \"m_Id\": \"eb0cede3a5064e43bd1ca69ef480ec60\"\n       
    },\n        {\n            \"m_Id\": \"a650976d00ac4bc78760002cadf39d1c\"\n       
    },\n        {\n            \"m_Id\": \"fbede82cbe61420d87bb2a70bd1336f6\"\n       
    },\n        {\n            \"m_Id\": \"bddc018d13634e6183f54d41b94c1abb\"\n       
    }\n    ],\n    \"m_Keywords\": [],\n    \"m_Dropdowns\": [],\n    \"m_CategoryData\":
    [\n        {\n            \"m_Id\": \"fed5074684984b96893bfa5fd7432a95\"\n       
    }\n    ],\n    \"m_Nodes\": [\n        {\n            \"m_Id\": \"e0de0a602ee24bfa926c07ca98b9c439\"\n       
    },\n        {\n            \"m_Id\": \"03399e6b5c74456385c1023f350a9a2b\"\n       
    },\n        {\n            \"m_Id\": \"ae626a243a3142b2a86d4b03efaa24c6\"\n       
    },\n        {\n            \"m_Id\": \"e36b851d54454c11b0f5622f3d358269\"\n       
    },\n        {\n            \"m_Id\": \"0113e4aad0ac408ca29aafec1e57298a\"\n       
    },\n        {\n            \"m_Id\": \"15cbf119bf1843f794b04dc31eec8ec2\"\n       
    },\n        {\n            \"m_Id\": \"448be26dc55c4a00871cb8c57a70ba6f\"\n       
    },\n        {\n            \"m_Id\": \"ff91fbe7a569454d8ae18f958fb4bf11\"\n       
    },\n        {\n            \"m_Id\": \"e98be26ceb8f465583ae0ec74fb7f5e2\"\n       
    },\n        {\n            \"m_Id\": \"bdeebcf3afad4153bf22fc7cc82c72dd\"\n       
    },\n        {\n            \"m_Id\": \"1c8c664def3f45a5837bd13acef1d16b\"\n       
    },\n        {\n            \"m_Id\": \"c91c7b3426df482eb9b6efcf475bd7b0\"\n       
    },\n        {\n            \"m_Id\": \"1c7977d08cc54c4692558f7108e66053\"\n       
    },\n        {\n            \"m_Id\": \"1774b2a8238b4973a1e3afd301330e7d\"\n       
    },\n        {\n            \"m_Id\": \"47e7e6f6c79c4f24bfc111c162c35f9f\"\n       
    },\n        {\n            \"m_Id\": \"5d7ce0c940ef40b49a4225123448caa2\"\n       
    },\n        {\n            \"m_Id\": \"0aa6747f00a3447f82b45b9e0cc9a545\"\n       
    },\n        {\n            \"m_Id\": \"41f9fdc7e1124614982621af88d5c8b0\"\n       
    },\n        {\n            \"m_Id\": \"15f052d5e01b45818927d368bf836863\"\n       
    },\n        {\n            \"m_Id\": \"3ab603e217ab4c3089acc1492753ca25\"\n       
    },\n        {\n            \"m_Id\": \"acec46d42a3547edb3a45d3f6f2c605f\"\n       
    },\n        {\n            \"m_Id\": \"dafbc94f76b74c2092e3bf0e915df7ee\"\n       
    },\n        {\n            \"m_Id\": \"4e2f08a2ec1945c4a61a6673c09f0834\"\n       
    },\n        {\n            \"m_Id\": \"9152e218683a4e7980ee92913e5a8a2b\"\n       
    },\n        {\n            \"m_Id\": \"aa0e93e097f048aeb1085c08c3d6b87a\"\n       
    },\n        {\n            \"m_Id\": \"7d7cd75b16a3458aa3274cd16c805d50\"\n       
    },\n        {\n            \"m_Id\": \"8d79a54a1cb3451e86bd511d107fb8a0\"\n       
    },\n        {\n            \"m_Id\": \"05b03d36153e42d59f0a9d9920da5054\"\n       
    },\n        {\n            \"m_Id\": \"03b460e3b1aa44a78be15ff860775597\"\n       
    },\n        {\n            \"m_Id\": \"e3cc90bcb3a44aee959bf0f0ca32b8e7\"\n       
    },\n        {\n            \"m_Id\": \"dab32ad82d9d43c6ba29cee5785d299a\"\n       
    },\n        {\n            \"m_Id\": \"d8d96fec3e4f4e04b8b06dec5da98063\"\n       
    },\n        {\n            \"m_Id\": \"246cd93130b64578a5fd002cf16d0835\"\n       
    },\n        {\n            \"m_Id\": \"0198e04950114f128528d879a5df87ff\"\n       
    },\n        {\n            \"m_Id\": \"918212dd43bf49a4839a7fbcbf4d3c9a\"\n       
    },\n        {\n            \"m_Id\": \"81161037fbf1419db60337228e6281d5\"\n       
    },\n        {\n            \"m_Id\": \"3ed4dcb24fe34ce4b245cf2eb144ec92\"\n       
    },\n        {\n            \"m_Id\": \"d943aacc20ec41efb86380dab6d412bf\"\n       
    }\n    ],\n    \"m_GroupDatas\": [\n        {\n            \"m_Id\": \"e11a09d5c60541f89ece3e930dc4c9d3\"\n       
    },\n        {\n            \"m_Id\": \"f7330befd4a1454598de51903e691fbc\"\n       
    },\n        {\n            \"m_Id\": \"f8cbdbe051ca4a71ab01d9dcc968e79e\"\n       
    },\n        {\n            \"m_Id\": \"3e1ac251b21e44f296d990278ea9918d\"\n       
    },\n        {\n            \"m_Id\": \"68c118ef40854618bdf9ddd8cf2ee290\"\n       
    },\n        {\n            \"m_Id\": \"fbd0a71e79964ac5a31cbfdcbee936d6\"\n       
    },\n        {\n            \"m_Id\": \"c061bd4ec10b4dcba2fa92f533b7c6a2\"\n       
    }\n    ],\n    \"m_StickyNoteDatas\": [\n        {\n            \"m_Id\": \"8a88626605fb4a21a8c370457ec0db0f\"\n       
    }\n    ],\n    \"m_Edges\": [\n        {\n            \"m_OutputSlot\": {\n               
    \"m_Node\": {\n                    \"m_Id\": \"0198e04950114f128528d879a5df87ff\"\n               
    },\n                \"m_SlotId\": 0\n            },\n            \"m_InputSlot\":
    {\n                \"m_Node\": {\n                    \"m_Id\": \"d8d96fec3e4f4e04b8b06dec5da98063\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"03b460e3b1aa44a78be15ff860775597\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"05b03d36153e42d59f0a9d9920da5054\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"05b03d36153e42d59f0a9d9920da5054\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"aa0e93e097f048aeb1085c08c3d6b87a\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"0aa6747f00a3447f82b45b9e0cc9a545\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"47e7e6f6c79c4f24bfc111c162c35f9f\"\n               
    },\n                \"m_SlotId\": 2\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"0aa6747f00a3447f82b45b9e0cc9a545\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"e3cc90bcb3a44aee959bf0f0ca32b8e7\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"15cbf119bf1843f794b04dc31eec8ec2\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"1c7977d08cc54c4692558f7108e66053\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"15cbf119bf1843f794b04dc31eec8ec2\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"e0de0a602ee24bfa926c07ca98b9c439\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"15cbf119bf1843f794b04dc31eec8ec2\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"03399e6b5c74456385c1023f350a9a2b\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"15cbf119bf1843f794b04dc31eec8ec2\"\n                },\n                \"m_SlotId\":
    4\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"41f9fdc7e1124614982621af88d5c8b0\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"15cbf119bf1843f794b04dc31eec8ec2\"\n                },\n                \"m_SlotId\":
    5\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"ff91fbe7a569454d8ae18f958fb4bf11\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"15cbf119bf1843f794b04dc31eec8ec2\"\n                },\n                \"m_SlotId\":
    7\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"9152e218683a4e7980ee92913e5a8a2b\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"15f052d5e01b45818927d368bf836863\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"0aa6747f00a3447f82b45b9e0cc9a545\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"1774b2a8238b4973a1e3afd301330e7d\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"bdeebcf3afad4153bf22fc7cc82c72dd\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"1c8c664def3f45a5837bd13acef1d16b\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"e3cc90bcb3a44aee959bf0f0ca32b8e7\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"246cd93130b64578a5fd002cf16d0835\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"d943aacc20ec41efb86380dab6d412bf\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"3ab603e217ab4c3089acc1492753ca25\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"15f052d5e01b45818927d368bf836863\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"3ed4dcb24fe34ce4b245cf2eb144ec92\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"246cd93130b64578a5fd002cf16d0835\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"448be26dc55c4a00871cb8c57a70ba6f\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"15cbf119bf1843f794b04dc31eec8ec2\"\n               
    },\n                \"m_SlotId\": 6\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"47e7e6f6c79c4f24bfc111c162c35f9f\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"e36b851d54454c11b0f5622f3d358269\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"4e2f08a2ec1945c4a61a6673c09f0834\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"15f052d5e01b45818927d368bf836863\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"5d7ce0c940ef40b49a4225123448caa2\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"47e7e6f6c79c4f24bfc111c162c35f9f\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"7d7cd75b16a3458aa3274cd16c805d50\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"05b03d36153e42d59f0a9d9920da5054\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"8d79a54a1cb3451e86bd511d107fb8a0\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"7d7cd75b16a3458aa3274cd16c805d50\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"aa0e93e097f048aeb1085c08c3d6b87a\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"15f052d5e01b45818927d368bf836863\"\n               
    },\n                \"m_SlotId\": 2\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"aa0e93e097f048aeb1085c08c3d6b87a\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"e3cc90bcb3a44aee959bf0f0ca32b8e7\"\n               
    },\n                \"m_SlotId\": 2\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"acec46d42a3547edb3a45d3f6f2c605f\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"dafbc94f76b74c2092e3bf0e915df7ee\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"bdeebcf3afad4153bf22fc7cc82c72dd\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"1c8c664def3f45a5837bd13acef1d16b\"\n               
    },\n                \"m_SlotId\": 2\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"c91c7b3426df482eb9b6efcf475bd7b0\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"1c8c664def3f45a5837bd13acef1d16b\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"d8d96fec3e4f4e04b8b06dec5da98063\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"246cd93130b64578a5fd002cf16d0835\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"d943aacc20ec41efb86380dab6d412bf\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"918212dd43bf49a4839a7fbcbf4d3c9a\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"dab32ad82d9d43c6ba29cee5785d299a\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"3ed4dcb24fe34ce4b245cf2eb144ec92\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"dafbc94f76b74c2092e3bf0e915df7ee\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"7d7cd75b16a3458aa3274cd16c805d50\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"e3cc90bcb3a44aee959bf0f0ca32b8e7\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"47e7e6f6c79c4f24bfc111c162c35f9f\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"e98be26ceb8f465583ae0ec74fb7f5e2\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"0aa6747f00a3447f82b45b9e0cc9a545\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        }\n    ],\n    \"m_VertexContext\":
    {\n        \"m_Position\": {\n            \"x\": 2414.74072265625,\n           
    \"y\": -139.48147583007813\n        },\n        \"m_Blocks\": [\n           
    {\n                \"m_Id\": \"e0de0a602ee24bfa926c07ca98b9c439\"\n           
    },\n            {\n                \"m_Id\": \"03399e6b5c74456385c1023f350a9a2b\"\n           
    },\n            {\n                \"m_Id\": \"ae626a243a3142b2a86d4b03efaa24c6\"\n           
    },\n            {\n                \"m_Id\": \"ff91fbe7a569454d8ae18f958fb4bf11\"\n           
    },\n            {\n                \"m_Id\": \"1c7977d08cc54c4692558f7108e66053\"\n           
    },\n            {\n                \"m_Id\": \"41f9fdc7e1124614982621af88d5c8b0\"\n           
    },\n            {\n                \"m_Id\": \"9152e218683a4e7980ee92913e5a8a2b\"\n           
    }\n        ]\n    },\n    \"m_FragmentContext\": {\n        \"m_Position\": {\n           
    \"x\": 2414.740966796875,\n            \"y\": 504.5185546875\n        },\n       
    \"m_Blocks\": [\n            {\n                \"m_Id\": \"e36b851d54454c11b0f5622f3d358269\"\n           
    },\n            {\n                \"m_Id\": \"0113e4aad0ac408ca29aafec1e57298a\"\n           
    },\n            {\n                \"m_Id\": \"918212dd43bf49a4839a7fbcbf4d3c9a\"\n           
    },\n            {\n                \"m_Id\": \"81161037fbf1419db60337228e6281d5\"\n           
    }\n        ]\n    },\n    \"m_PreviewData\": {\n        \"serializedMesh\": {\n           
    \"m_SerializedMesh\": \"{\\\"mesh\\\":{\\\"instanceID\\\":0}}\",\n           
    \"m_Guid\": \"\"\n        },\n        \"preventRotation\": false\n    },\n   
    \"m_Path\": \"Shader Graphs\",\n    \"m_GraphPrecision\": 1,\n    \"m_PreviewMode\":
    2,\n    \"m_OutputNode\": {\n        \"m_Id\": \"\"\n    },\n    \"m_SubDatas\":
    [],\n    \"m_ActiveTargets\": [\n        {\n            \"m_Id\": \"ab1abb471bb94af38546e54a602a2ae2\"\n       
    }\n    ]\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"001912cf92194c369355f61b361fe313\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 2.0,\n        \"e01\": 2.0,\n        \"e02\": 2.0,\n       
    \"e03\": 2.0,\n        \"e10\": 2.0,\n        \"e11\": 2.0,\n        \"e12\":
    2.0,\n        \"e13\": 2.0,\n        \"e20\": 2.0,\n        \"e21\": 2.0,\n       
    \"e22\": 2.0,\n        \"e23\": 2.0,\n        \"e30\": 2.0,\n        \"e31\":
    2.0,\n        \"e32\": 2.0,\n        \"e33\": 2.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"0113e4aad0ac408ca29aafec1e57298a\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.Smoothness\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"93a7a91849a348f381f429957841e6ac\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.Smoothness\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.CustomInterpolatorNode\",\n    \"m_ObjectId\": \"0198e04950114f128528d879a5df87ff\",\n   
    \"m_Group\": {\n        \"m_Id\": \"c061bd4ec10b4dcba2fa92f533b7c6a2\"\n    },\n   
    \"m_Name\": \"ExtraBufferData (Custom Interpolator)\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 1688.296142578125,\n            \"y\": 1567.4814453125,\n           
    \"width\": 262.888916015625,\n            \"height\": 278.4443359375\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"1beb269d43f848f88721669ccdd47a21\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"customBlockNodeName\":
    \"ExtraBufferData\",\n    \"serializedType\": 4\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"03399e6b5c74456385c1023f350a9a2b\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"VertexDescription.Normal\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"9ad8b5a1ba904e8ca03a67c5e09d748c\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.Normal\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\": \"03b460e3b1aa44a78be15ff860775597\",\n   
    \"m_Group\": {\n        \"m_Id\": \"f8cbdbe051ca4a71ab01d9dcc968e79e\"\n    },\n   
    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -207.92601013183595,\n            \"y\": 1030.8148193359375,\n            \"width\":
    122.37042236328125,\n            \"height\": 34.2220458984375\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"7696228a2ef04cb48689f7f921f17d0a\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"bddc018d13634e6183f54d41b94c1abb\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"05700e0b5130459d8c2f28bda0a227d8\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.AddNode\",\n    \"m_ObjectId\": \"05b03d36153e42d59f0a9d9920da5054\",\n   
    \"m_Group\": {\n        \"m_Id\": \"f8cbdbe051ca4a71ab01d9dcc968e79e\"\n    },\n   
    \"m_Name\": \"Add\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    79.3332748413086,\n            \"y\": 992.9629516601563,\n            \"width\":
    128.07406616210938,\n            \"height\": 118.74053955078125\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"32ac49ec34b84366b6de04a92db92877\"\n       
    },\n        {\n            \"m_Id\": \"05700e0b5130459d8c2f28bda0a227d8\"\n       
    },\n        {\n            \"m_Id\": \"283137f1f5e7482691d9028ee5bb17e3\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"addition\",\n        \"sum\",\n       
    \"plus\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": false,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n    \"m_ObjectId\":
    \"0758f5b8b2944852b0e7c8eb36381f04\",\n    \"m_Id\": 7,\n    \"m_DisplayName\":
    \"ExtraBuffer\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"ExtraBuffer\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n       
    \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n   
    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.MultiplyNode\",\n    \"m_ObjectId\":
    \"0aa6747f00a3447f82b45b9e0cc9a545\",\n    \"m_Group\": {\n        \"m_Id\":
    \"3e1ac251b21e44f296d990278ea9918d\"\n    },\n    \"m_Name\": \"Multiply\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 1183.0,\n            \"y\":
    302.9999694824219,\n            \"width\": 130.0,\n            \"height\": 118.0\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"7580a60941e54ebe83535ea20828bd30\"\n       
    },\n        {\n            \"m_Id\": \"001912cf92194c369355f61b361fe313\"\n       
    },\n        {\n            \"m_Id\": \"2afcc5036f024860b4988baa390e2627\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"multiplication\",\n        \"times\",\n       
    \"x\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": false,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"0f97e65d10984dde9eaf08770311dbc7\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"R\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"R\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 1,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.CustomFunctionNode\",\n    \"m_ObjectId\": \"15cbf119bf1843f794b04dc31eec8ec2\",\n   
    \"m_Group\": {\n        \"m_Id\": \"e11a09d5c60541f89ece3e930dc4c9d3\"\n    },\n   
    \"m_Name\": \"GetComputeData (Custom Function)\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 865.6000366210938,\n            \"y\": -245.60000610351563,\n           
    \"width\": 254.40008544921876,\n            \"height\": 189.60000610351563\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"c106a69d9827455b85a72d0ab81fa03d\"\n       
    },\n        {\n            \"m_Id\": \"8603ec986093408d895249ef7443feac\"\n       
    },\n        {\n            \"m_Id\": \"45be0b118e284a8e9336a0a59b683fdd\"\n       
    },\n        {\n            \"m_Id\": \"a6d75786e40e49dd82abc31c305fb4ea\"\n       
    },\n        {\n            \"m_Id\": \"e579c6e921594f36b989619a164c7812\"\n       
    },\n        {\n            \"m_Id\": \"0758f5b8b2944852b0e7c8eb36381f04\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"code\",\n        \"HLSL\"\n    ],\n   
    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": false,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    },\n    \"m_SourceType\": 0,\n    \"m_FunctionName\": \"GetComputeData\",\n   
    \"m_FunctionSource\": \"b5ddc3e08a921514cae2c808821a62e0\",\n    \"m_FunctionSourceUsePragmas\":
    true,\n    \"m_FunctionBody\": \"Enter function body here...\"\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.LerpNode\",\n    \"m_ObjectId\":
    \"15f052d5e01b45818927d368bf836863\",\n    \"m_Group\": {\n        \"m_Id\":
    \"f8cbdbe051ca4a71ab01d9dcc968e79e\"\n    },\n    \"m_Name\": \"Lerp\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 615.4813232421875,\n           
    \"y\": 666.814697265625,\n            \"width\": 132.2222900390625,\n           
    \"height\": 142.592529296875\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"de720166574d4fecb89f5396b679756c\"\n        },\n       
    {\n            \"m_Id\": \"8de35904bb62424ea26d0ea1d1076408\"\n        },\n       
    {\n            \"m_Id\": \"6e916752edd34cc9b11d243a49e48681\"\n        },\n       
    {\n            \"m_Id\": \"184d8f1e7a3a4ff286aa59560f6fe3ec\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"mix\",\n        \"blend\",\n        \"linear
    interpolate\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": false,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.Texture2DShaderProperty\",\n   
    \"m_ObjectId\": \"163bfa18e4ab4e75b93815703c941ea0\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"55613b39-cbab-4e83-a7a4-1f6527e5c4fb\"\n    },\n    \"m_Name\":
    \"BlendingTexture\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"BlendingTexture\",\n    \"m_DefaultReferenceName\": \"_BlendingTexture\",\n   
    \"m_OverrideReferenceName\": \"_TerrainDiffuse\",\n    \"m_GeneratePropertyBlock\":
    false,\n    \"m_UseCustomSlotLabel\": false,\n    \"m_CustomSlotLabel\": \"\",\n   
    \"m_DismissedVersion\": 0,\n    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\":
    false,\n    \"hlslDeclarationOverride\": 0,\n    \"m_Hidden\": false,\n    \"m_Value\":
    {\n        \"m_SerializedTexture\": \"\",\n        \"m_Guid\": \"\"\n    },\n   
    \"isMainTexture\": false,\n    \"useTilingAndOffset\": false,\n    \"m_Modifiable\":
    true,\n    \"m_DefaultType\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.CustomInterpolatorNode\",\n    \"m_ObjectId\": \"1774b2a8238b4973a1e3afd301330e7d\",\n   
    \"m_Group\": {\n        \"m_Id\": \"f7330befd4a1454598de51903e691fbc\"\n    },\n   
    \"m_Name\": \"WorldPos (Custom Interpolator)\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -85.99999237060547,\n            \"y\": 95.9999771118164,\n           
    \"width\": 226.0,\n            \"height\": 94.0000228881836\n        }\n    },\n   
    \"m_Slots\": [\n        {\n            \"m_Id\": \"1dae4bd7deb041e9899e0264fcbd0375\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    false,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"customBlockNodeName\":
    \"WorldPos\",\n    \"serializedType\": 3\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"17ea50cf4c764e01a91d6ca1ed7a25a7\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 1.0,\n       
    \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\": 1.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"184d8f1e7a3a4ff286aa59560f6fe3ec\",\n    \"m_Id\": 3,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n    \"m_ObjectId\":
    \"1995a3af76b34e94b10de4320bb8002c\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"worldPos\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"worldPos\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SamplerStateMaterialSlot\",\n   
    \"m_ObjectId\": \"1ad38ed8b70644de923e77c037b03697\",\n    \"m_Id\": 3,\n   
    \"m_DisplayName\": \"Sampler\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Sampler\",\n    \"m_StageCapability\": 3,\n    \"m_BareResource\":
    false\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n   
    \"m_ObjectId\": \"1beb269d43f848f88721669ccdd47a21\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"1c7977d08cc54c4692558f7108e66053\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"VertexDescription.CustomInterpolator\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"e6d367a506624bf3bea700626e0a9a06\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.WorldPos#3\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.SampleTexture2DNode\",\n    \"m_ObjectId\": \"1c8c664def3f45a5837bd13acef1d16b\",\n   
    \"m_Group\": {\n        \"m_Id\": \"f7330befd4a1454598de51903e691fbc\"\n    },\n   
    \"m_Name\": \"Sample Texture 2D\",\n    \"m_DrawState\": {\n        \"m_Expanded\":
    true,\n        \"m_Position\": {\n            \"serializedVersion\": \"2\",\n           
    \"x\": 429.0,\n            \"y\": -8.000008583068848,\n            \"width\":
    208.00006103515626,\n            \"height\": 435.0000305175781\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"6ad6280cc37c4f3097fdb6ba020866c8\"\n       
    },\n        {\n            \"m_Id\": \"9ba409b06082421c98c3acfbae851966\"\n       
    },\n        {\n            \"m_Id\": \"4d34100a86794cba8cf2b96676ec0982\"\n       
    },\n        {\n            \"m_Id\": \"71f8ca1682004caa9f72da9cce35f2ed\"\n       
    },\n        {\n            \"m_Id\": \"5e82433075024bf68a88754dc8639eb8\"\n       
    },\n        {\n            \"m_Id\": \"5b06b83ad6ed4c40a2f54d3531c3975a\"\n       
    },\n        {\n            \"m_Id\": \"a6b2aeec98aa4ccd876c71a2cfb2530b\"\n       
    },\n        {\n            \"m_Id\": \"1ad38ed8b70644de923e77c037b03697\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"tex2d\"\n    ],\n    \"m_Precision\":
    0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    },\n   
    \"m_TextureType\": 0,\n    \"m_NormalMapSpace\": 0,\n    \"m_EnableGlobalMipBias\":
    true,\n    \"m_MipSamplingMode\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n    \"m_ObjectId\": \"1dae4bd7deb041e9899e0264fcbd0375\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 2,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n   
    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.BooleanShaderProperty\",\n   
    \"m_ObjectId\": \"217ecc1b3f414f6298c540a27a883e11\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"60569da5-f1b7-43c0-9686-0c166bee45ff\"\n    },\n    \"m_Name\":
    \"Blend\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"Blend\",\n    \"m_DefaultReferenceName\": \"_Blend\",\n    \"m_OverrideReferenceName\":
    \"\",\n    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": false\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BooleanMaterialSlot\",\n    \"m_ObjectId\":
    \"23c56da0ff964aa49320ba35f9aeeca1\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Predicate\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Predicate\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": false,\n    \"m_DefaultValue\":
    false\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.StepNode\",\n   
    \"m_ObjectId\": \"246cd93130b64578a5fd002cf16d0835\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Step\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 2228.59228515625,\n            \"y\": 1305.629638671875,\n           
    \"width\": 147.25927734375,\n            \"height\": 118.22216796875\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"6470698f2975464fb9dff035e6fc18cf\"\n       
    },\n        {\n            \"m_Id\": \"55f7f682e3ba4fbb90c7df42a82dae9c\"\n       
    },\n        {\n            \"m_Id\": \"270776b5dbc04c34aaff2c0d7f7e4a0e\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    false,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"270776b5dbc04c34aaff2c0d7f7e4a0e\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"283137f1f5e7482691d9028ee5bb17e3\",\n    \"m_Id\": 2,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"2afcc5036f024860b4988baa390e2627\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"32ac49ec34b84366b6de04a92db92877\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"35aaf57c8c584b1dac3ead46ba48187e\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Alpha Clip Threshold\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"AlphaClipThreshold\",\n    \"m_StageCapability\": 2,\n   
    \"m_Value\": 0.8999999761581421,\n    \"m_DefaultValue\": 0.5,\n    \"m_Labels\":
    []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n   
    \"m_ObjectId\": \"3ab603e217ab4c3089acc1492753ca25\",\n    \"m_Group\": {\n       
    \"m_Id\": \"f8cbdbe051ca4a71ab01d9dcc968e79e\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 189.25918579101563,\n           
    \"y\": 688.592529296875,\n            \"width\": 108.37039184570313,\n           
    \"height\": 34.22222900390625\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"445e4cbd65f94d57a69376dd3dd4a469\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"eb0cede3a5064e43bd1ca69ef480ec60\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BooleanMaterialSlot\",\n    \"m_ObjectId\":
    \"3ae524092e294f299c98923226d093a2\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Blend\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": false,\n    \"m_DefaultValue\":
    false\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GroupData\",\n   
    \"m_ObjectId\": \"3e1ac251b21e44f296d990278ea9918d\",\n    \"m_Title\": \"Grass
    Painted Color\",\n    \"m_Position\": {\n        \"x\": 724.7999877929688,\n       
    \"y\": 144.8000030517578\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.SplitNode\",\n    \"m_ObjectId\": \"3ed4dcb24fe34ce4b245cf2eb144ec92\",\n   
    \"m_Group\": {\n        \"m_Id\": \"fbd0a71e79964ac5a31cbfdcbee936d6\"\n    },\n   
    \"m_Name\": \"Split\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    1996.296142578125,\n            \"y\": 1176.0,\n            \"width\": 120.815185546875,\n           
    \"height\": 149.333251953125\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"cda5210a851c49e5a3a84ec59e7532d8\"\n        },\n       
    {\n            \"m_Id\": \"ba1697f115ce4e538f69522bcff82e71\"\n        },\n       
    {\n            \"m_Id\": \"4d669290e2384643b19e36ffeeb76d54\"\n        },\n       
    {\n            \"m_Id\": \"74d6df82c2c34389ad2df32af86d5349\"\n        },\n       
    {\n            \"m_Id\": \"994c3199b84d4998ae61e7ec1cc90915\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"separate\"\n    ],\n    \"m_Precision\": 0,\n   
    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"40888c60b301464496a24bfc71f18ed6\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"41690661027b400e8e0c0605203322a0\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n   
    \"m_ObjectId\": \"41f9fdc7e1124614982621af88d5c8b0\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"VertexDescription.CustomInterpolator\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"b57e6651baa64b138dac5cb97759aa62\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.UV#2\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n    \"m_ObjectId\": \"445e4cbd65f94d57a69376dd3dd4a469\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"TopTint\",\n    \"m_SlotType\": 1,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\":
    3,\n    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\":
    []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.VertexIDNode\",\n   
    \"m_ObjectId\": \"448be26dc55c4a00871cb8c57a70ba6f\",\n    \"m_Group\": {\n       
    \"m_Id\": \"e11a09d5c60541f89ece3e930dc4c9d3\"\n    },\n    \"m_Name\": \"Vertex
    ID\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\":
    {\n            \"serializedVersion\": \"2\",\n            \"x\": 728.800048828125,\n           
    \"y\": -227.20001220703126,\n            \"width\": 99.20001220703125,\n           
    \"height\": 76.80001831054688\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"524ed613537741ac955bdfe67a3eaba5\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"45b0227909644d69a7520029101b6292\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n    \"m_ObjectId\":
    \"45be0b118e284a8e9336a0a59b683fdd\",\n    \"m_Id\": 3,\n    \"m_DisplayName\":
    \"normal\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"normal\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BranchNode\",\n   
    \"m_ObjectId\": \"47e7e6f6c79c4f24bfc111c162c35f9f\",\n    \"m_Group\": {\n       
    \"m_Id\": \"68c118ef40854618bdf9ddd8cf2ee290\"\n    },\n    \"m_Name\": \"Branch\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 1597.0,\n            \"y\":
    272.9999694824219,\n            \"width\": 172.0001220703125,\n            \"height\":
    142.0\n        }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\":
    \"23c56da0ff964aa49320ba35f9aeeca1\"\n        },\n        {\n            \"m_Id\":
    \"90cd368f8caf478ba18dacf5dc4e7c07\"\n        },\n        {\n            \"m_Id\":
    \"5ee9b320419940d899eb81abf0bf7440\"\n        },\n        {\n            \"m_Id\":
    \"e576d49be7954eda9f15f2bd252c52a5\"\n        }\n    ],\n    \"synonyms\": [\n       
    \"switch\",\n        \"if\",\n        \"else\"\n    ],\n    \"m_Precision\":
    0,\n    \"m_PreviewExpanded\": false,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"4d34100a86794cba8cf2b96676ec0982\",\n    \"m_Id\": 5,\n   
    \"m_DisplayName\": \"G\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"G\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"4d669290e2384643b19e36ffeeb76d54\",\n    \"m_Id\": 2,\n    \"m_DisplayName\":
    \"G\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"G\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"4da8698a7871442a9afd4984905d657a\",\n   
    \"m_Id\": 4,\n    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n   
    \"m_ObjectId\": \"4e2f08a2ec1945c4a61a6673c09f0834\",\n    \"m_Group\": {\n       
    \"m_Id\": \"f8cbdbe051ca4a71ab01d9dcc968e79e\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 172.1480712890625,\n           
    \"y\": 632.5926513671875,\n            \"width\": 125.99993896484375,\n           
    \"height\": 34.2220458984375\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"d207c15ba8ce454da20a997734945c62\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"a650976d00ac4bc78760002cadf39d1c\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"524ed613537741ac955bdfe67a3eaba5\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 1,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"55f7f682e3ba4fbb90c7df42a82dae9c\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Texture2DInputMaterialSlot\",\n   
    \"m_ObjectId\": \"5b06b83ad6ed4c40a2f54d3531c3975a\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"Texture\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Texture\",\n    \"m_StageCapability\": 3,\n    \"m_BareResource\":
    false,\n    \"m_Texture\": {\n        \"m_SerializedTexture\": \"\",\n       
    \"m_Guid\": \"\"\n    },\n    \"m_DefaultType\": 0\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\":
    \"5d7ce0c940ef40b49a4225123448caa2\",\n    \"m_Group\": {\n        \"m_Id\":
    \"68c118ef40854618bdf9ddd8cf2ee290\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 1404.0,\n            \"y\":
    194.9999542236328,\n            \"width\": 107.0,\n            \"height\": 34.00001525878906\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"3ae524092e294f299c98923226d093a2\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"217ecc1b3f414f6298c540a27a883e11\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"5e82433075024bf68a88754dc8639eb8\",\n    \"m_Id\": 7,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"5ee9b320419940d899eb81abf0bf7440\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"False\",\n    \"m_SlotType\": 0,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"False\",\n    \"m_StageCapability\":
    3,\n    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"61bf837a21d64bc2b6aa5c5f47ceaeb4\",\n    \"m_Id\": 3,\n    \"m_DisplayName\":
    \"B\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"6470698f2975464fb9dff035e6fc18cf\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"Edge\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Edge\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n       
    \"w\": 1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GroupData\",\n    \"m_ObjectId\":
    \"68c118ef40854618bdf9ddd8cf2ee290\",\n    \"m_Title\": \"Blend Switch\",\n   
    \"m_Position\": {\n        \"x\": 1378.4000244140625,\n        \"y\": 135.20001220703126\n   
    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n   
    \"m_ObjectId\": \"6ad6280cc37c4f3097fdb6ba020866c8\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"RGBA\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"RGBA\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"6e916752edd34cc9b11d243a49e48681\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"T\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"T\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"71f8ca1682004caa9f72da9cce35f2ed\",\n    \"m_Id\": 6,\n    \"m_DisplayName\":
    \"B\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"B\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Texture2DMaterialSlot\",\n    \"m_ObjectId\": \"72068a79ff774bf2ae6b3b16407c690f\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"BlendingTexture\",\n    \"m_SlotType\":
    1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\":
    3,\n    \"m_BareResource\": false\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"74d6df82c2c34389ad2df32af86d5349\",\n   
    \"m_Id\": 3,\n    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"7580a60941e54ebe83535ea20828bd30\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"7696228a2ef04cb48689f7f921f17d0a\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"BlendOff\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\": \"7af4459e0ec542e3b4c9b35ef89234bb\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"e00\": 2.0,\n        \"e01\": 2.0,\n        \"e02\":
    2.0,\n        \"e03\": 2.0,\n        \"e10\": 2.0,\n        \"e11\": 2.0,\n       
    \"e12\": 2.0,\n        \"e13\": 2.0,\n        \"e20\": 2.0,\n        \"e21\":
    2.0,\n        \"e22\": 2.0,\n        \"e23\": 2.0,\n        \"e30\": 2.0,\n       
    \"e31\": 2.0,\n        \"e32\": 2.0,\n        \"e33\": 2.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.MultiplyNode\",\n    \"m_ObjectId\":
    \"7d7cd75b16a3458aa3274cd16c805d50\",\n    \"m_Group\": {\n        \"m_Id\":
    \"f8cbdbe051ca4a71ab01d9dcc968e79e\"\n    },\n    \"m_Name\": \"Multiply\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -57.55557632446289,\n           
    \"y\": 1110.6666259765625,\n            \"width\": 128.073974609375,\n           
    \"height\": 118.7406005859375\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"96c83fb0865b49fa8e545bf8f51c8b93\"\n        },\n       
    {\n            \"m_Id\": \"7af4459e0ec542e3b4c9b35ef89234bb\"\n        },\n       
    {\n            \"m_Id\": \"9016835b29fb4a349c9f1b1a95fd5536\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"multiplication\",\n        \"times\",\n       
    \"x\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": false,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"81161037fbf1419db60337228e6281d5\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.AlphaClipThreshold\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"35aaf57c8c584b1dac3ead46ba48187e\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.AlphaClipThreshold\"\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n    \"m_ObjectId\":
    \"8603ec986093408d895249ef7443feac\",\n    \"m_Id\": 2,\n    \"m_DisplayName\":
    \"worldPos\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"worldPos\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.StickyNoteData\",\n   
    \"m_ObjectId\": \"8a88626605fb4a21a8c370457ec0db0f\",\n    \"m_Title\": \"Shadows?\",\n   
    \"m_Content\": \"You can set this Graph to Lit in the Graph settings to enable
    shadows\",\n    \"m_TextSize\": 0,\n    \"m_Theme\": 0,\n    \"m_Position\":
    {\n        \"serializedVersion\": \"2\",\n        \"x\": 2123.555419921875,\n       
    \"y\": -308.4444580078125,\n        \"width\": 200.0,\n        \"height\": 160.00001525878907\n   
    },\n    \"m_Group\": {\n        \"m_Id\": \"\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\":
    \"8d79a54a1cb3451e86bd511d107fb8a0\",\n    \"m_Group\": {\n        \"m_Id\":
    \"f8cbdbe051ca4a71ab01d9dcc968e79e\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -166.96295166015626,\n           
    \"y\": 997.6295166015625,\n            \"width\": 128.59251403808595,\n           
    \"height\": 34.22216796875\n        }\n    },\n    \"m_Slots\": [\n        {\n           
    \"m_Id\": \"b690a3a8c17244bebeadfeb112f5cbfb\"\n        }\n    ],\n    \"synonyms\":
    [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    },\n    \"m_Property\": {\n        \"m_Id\": \"fbede82cbe61420d87bb2a70bd1336f6\"\n   
    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"8de35904bb62424ea26d0ea1d1076408\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\":
    \"9016835b29fb4a349c9f1b1a95fd5536\",\n    \"m_Id\": 2,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"e00\":
    0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n        \"e03\": 0.0,\n       
    \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\": 0.0,\n        \"e13\":
    0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n        \"e22\": 0.0,\n       
    \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\": 0.0,\n        \"e32\":
    0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"e00\":
    1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n        \"e03\": 0.0,\n       
    \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\": 0.0,\n        \"e13\":
    0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n        \"e22\": 1.0,\n       
    \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\": 0.0,\n        \"e32\":
    0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"90cd368f8caf478ba18dacf5dc4e7c07\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"True\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"True\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n       
    \"w\": 1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"9152e218683a4e7980ee92913e5a8a2b\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"VertexDescription.CustomInterpolator\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"b89dcdbc6e1742e8be97ebfed1be2ae5\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.ExtraBufferData#4\"\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\": \"918212dd43bf49a4839a7fbcbf4d3c9a\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.Alpha\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"944630a127f4464480903ee19c904ee1\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.Alpha\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"93a7a91849a348f381f429957841e6ac\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"Smoothness\",\n    \"m_SlotType\": 0,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Smoothness\",\n    \"m_StageCapability\":
    2,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.5,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"944630a127f4464480903ee19c904ee1\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Alpha\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Alpha\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    1.0,\n    \"m_DefaultValue\": 1.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"96c83fb0865b49fa8e545bf8f51c8b93\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"994c3199b84d4998ae61e7ec1cc90915\",\n    \"m_Id\": 4,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"9abf30583b144ac6b0d2fcefc76ffd57\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.NormalMaterialSlot\",\n    \"m_ObjectId\":
    \"9ad8b5a1ba904e8ca03a67c5e09d748c\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Normal\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Normal\",\n    \"m_StageCapability\": 1,\n    \"m_Value\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_Labels\": [],\n    \"m_Space\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"9ba409b06082421c98c3acfbae851966\",\n   
    \"m_Id\": 4,\n    \"m_DisplayName\": \"R\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"R\",\n    \"m_StageCapability\": 2,\n   
    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.TangentMaterialSlot\",\n   
    \"m_ObjectId\": \"9bc53d5637284a208eb916bc52bfa7f0\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Tangent\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Tangent\",\n    \"m_StageCapability\": 1,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0\n    },\n    \"m_Labels\": [],\n    \"m_Space\": 0\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n    \"m_ObjectId\":
    \"9ebba7b24f0843bbac3aa7d35ea04402\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"a30abab27b1d48e68ec3c90fb4c236c7\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 3,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.ColorShaderProperty\",\n    \"m_ObjectId\":
    \"a650976d00ac4bc78760002cadf39d1c\",\n    \"m_Guid\": {\n        \"m_GuidSerialized\":
    \"c54b7a03-a7d8-496d-a78a-76cf9d7c3669\"\n    },\n    \"m_Name\": \"BottomTint\",\n   
    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\": \"BottomTint\",\n   
    \"m_DefaultReferenceName\": \"_BottomTint\",\n    \"m_OverrideReferenceName\":
    \"\",\n    \"m_GeneratePropertyBlock\": false,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": {\n        \"r\": 0.0,\n       
    \"g\": 0.0,\n        \"b\": 0.0,\n        \"a\": 0.0\n    },\n    \"isMainColor\":
    false,\n    \"m_ColorMode\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.UVMaterialSlot\",\n    \"m_ObjectId\": \"a6b2aeec98aa4ccd876c71a2cfb2530b\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"UV\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"UV\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0\n    },\n    \"m_Labels\": [],\n   
    \"m_Channel\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector2MaterialSlot\",\n   
    \"m_ObjectId\": \"a6d75786e40e49dd82abc31c305fb4ea\",\n    \"m_Id\": 4,\n   
    \"m_DisplayName\": \"uv\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"uv\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0\n    },\n    \"m_DefaultValue\": {\n       
    \"x\": 0.0,\n        \"y\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SaturateNode\",\n    \"m_ObjectId\":
    \"aa0e93e097f048aeb1085c08c3d6b87a\",\n    \"m_Group\": {\n        \"m_Id\":
    \"f8cbdbe051ca4a71ab01d9dcc968e79e\"\n    },\n    \"m_Name\": \"Saturate\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 241.1110382080078,\n           
    \"y\": 984.6665649414063,\n            \"width\": 130.1481170654297,\n           
    \"height\": 94.88885498046875\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"41690661027b400e8e0c0605203322a0\"\n        },\n       
    {\n            \"m_Id\": \"e2d4ae6af501445b977f333cd38e8cd8\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"clamp\"\n    ],\n    \"m_Precision\": 0,\n   
    \"m_PreviewExpanded\": false,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 1,\n    \"m_Type\": \"UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget\",\n   
    \"m_ObjectId\": \"ab1abb471bb94af38546e54a602a2ae2\",\n    \"m_Datas\": [],\n   
    \"m_ActiveSubTarget\": {\n        \"m_Id\": \"d9e748a6c55643dda4e8b1b8c673a9f7\"\n   
    },\n    \"m_AllowMaterialOverride\": false,\n    \"m_SurfaceType\": 0,\n    \"m_ZTestMode\":
    4,\n    \"m_ZWriteControl\": 0,\n    \"m_AlphaMode\": 0,\n    \"m_RenderFace\":
    0,\n    \"m_AlphaClip\": true,\n    \"m_CastShadows\": true,\n    \"m_ReceiveShadows\":
    true,\n    \"m_DisableTint\": false,\n    \"m_AdditionalMotionVectorMode\": 0,\n   
    \"m_AlembicMotionVectors\": false,\n    \"m_SupportsLODCrossFade\": false,\n   
    \"m_CustomEditorGUI\": \"\",\n    \"m_SupportVFX\": false\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"abac69a168ad4460be7b0e686a840776\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.CustomInterpolatorNode\",\n    \"m_ObjectId\":
    \"acec46d42a3547edb3a45d3f6f2c605f\",\n    \"m_Group\": {\n        \"m_Id\":
    \"f8cbdbe051ca4a71ab01d9dcc968e79e\"\n    },\n    \"m_Name\": \"UV (Custom Interpolator)\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -212.5926055908203,\n           
    \"y\": 715.0369262695313,\n            \"width\": 189.25924682617188,\n           
    \"height\": 94.88885498046875\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"e0ec5df97b7c4bea9e85606ead9eb6cd\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    false,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"customBlockNodeName\":
    \"UV\",\n    \"serializedType\": 2\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\": \"ae626a243a3142b2a86d4b03efaa24c6\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"VertexDescription.Tangent\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"9bc53d5637284a208eb916bc52bfa7f0\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.Tangent\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.ColorRGBMaterialSlot\",\n    \"m_ObjectId\": \"b24eb957f7b5417b801370ac1855dd49\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"Base Color\",\n    \"m_SlotType\": 0,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"BaseColor\",\n    \"m_StageCapability\":
    2,\n    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0\n    },\n    \"m_Labels\": [],\n    \"m_ColorMode\": 0,\n    \"m_DefaultColor\":
    {\n        \"r\": 0.5,\n        \"g\": 0.5,\n        \"b\": 0.5,\n        \"a\":
    1.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"b27e8be40a784e9a8493fc1daa47c922\",\n    \"m_Id\": 4,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PositionMaterialSlot\",\n    \"m_ObjectId\":
    \"b2aaac1d2768467abd81d5b2cecdebfd\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Position\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Position\",\n    \"m_StageCapability\": 1,\n    \"m_Value\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_Labels\": [],\n    \"m_Space\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector2MaterialSlot\",\n    \"m_ObjectId\": \"b57e6651baa64b138dac5cb97759aa62\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"UV\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"UV\",\n    \"m_StageCapability\": 1,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"b690a3a8c17244bebeadfeb112f5cbfb\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"BlendMult\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n   
    \"m_ObjectId\": \"b89dcdbc6e1742e8be97ebfed1be2ae5\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"ExtraBufferData\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"ExtraBufferData\",\n    \"m_StageCapability\":
    1,\n    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_Labels\":
    []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"ba1697f115ce4e538f69522bcff82e71\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"R\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"R\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"bd545a81db974ca09133f6b563e0dc32\",\n    \"m_Id\": 3,\n    \"m_DisplayName\":
    \"B\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 1,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty\",\n    \"m_ObjectId\":
    \"bddc018d13634e6183f54d41b94c1abb\",\n    \"m_Guid\": {\n        \"m_GuidSerialized\":
    \"83a39ac9-7567-495b-b04a-bfaef747ebdc\"\n    },\n    \"m_Name\": \"BlendOff\",\n   
    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\": \"BlendOff\",\n   
    \"m_DefaultReferenceName\": \"_BlendOff\",\n    \"m_OverrideReferenceName\":
    \"\",\n    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": 0.0,\n    \"m_FloatType\": 0,\n   
    \"m_RangeValues\": {\n        \"x\": 0.0,\n        \"y\": 1.0\n    }\n}\n\n{\n   
    \"m_SGVersion\": 1,\n    \"m_Type\": \"UnityEditor.ShaderGraph.CustomFunctionNode\",\n   
    \"m_ObjectId\": \"bdeebcf3afad4153bf22fc7cc82c72dd\",\n    \"m_Group\": {\n       
    \"m_Id\": \"f7330befd4a1454598de51903e691fbc\"\n    },\n    \"m_Name\": \"GetWorldUV
    (Custom Function)\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    166.00001525878907,\n            \"y\": 86.99999237060547,\n            \"width\":
    225.00001525878907,\n            \"height\": 278.0000305175781\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"1995a3af76b34e94b10de4320bb8002c\"\n       
    },\n        {\n            \"m_Id\": \"f41b0c2e2b02488da19963d8cf1789d5\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"code\",\n        \"HLSL\"\n    ],\n   
    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    },\n    \"m_SourceType\": 0,\n    \"m_FunctionName\": \"GetWorldUV\",\n   
    \"m_FunctionSource\": \"b5ddc3e08a921514cae2c808821a62e0\",\n    \"m_FunctionSourceUsePragmas\":
    true,\n    \"m_FunctionBody\": \"Enter function body here...\"\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"bdf41e729e12496cbaa767bd9974b49b\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"R\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"R\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.GroupData\",\n    \"m_ObjectId\": \"c061bd4ec10b4dcba2fa92f533b7c6a2\",\n   
    \"m_Title\": \"Cutbuffer World Position Y\",\n    \"m_Position\": {\n       
    \"x\": 1662.4000244140625,\n        \"y\": 1507.199951171875\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"c106a69d9827455b85a72d0ab81fa03d\",\n    \"m_Id\": 6,\n   
    \"m_DisplayName\": \"VertexID\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"VertexID\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\":
    \"c91c7b3426df482eb9b6efcf475bd7b0\",\n    \"m_Group\": {\n        \"m_Id\":
    \"f7330befd4a1454598de51903e691fbc\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 217.00003051757813,\n           
    \"y\": -29.00002670288086,\n            \"width\": 159.99993896484376,\n           
    \"height\": 34.00000762939453\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"72068a79ff774bf2ae6b3b16407c690f\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"163bfa18e4ab4e75b93815703c941ea0\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n    \"m_ObjectId\":
    \"cb699555ee87458b91ba73b3e48934bb\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"ColorPaint\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"ColorPaint\",\n    \"m_StageCapability\": 1,\n    \"m_Value\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"cda5210a851c49e5a3a84ec59e7532d8\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector4MaterialSlot\",\n    \"m_ObjectId\":
    \"d207c15ba8ce454da20a997734945c62\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"BottomTint\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"d56c68dc47a04c59b27323ce6d604be4\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"G\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"G\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"d6a27e12cd20436fb5403677a3bd9c5d\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"G\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"G\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SplitNode\",\n    \"m_ObjectId\":
    \"d8d96fec3e4f4e04b8b06dec5da98063\",\n    \"m_Group\": {\n        \"m_Id\":
    \"c061bd4ec10b4dcba2fa92f533b7c6a2\"\n    },\n    \"m_Name\": \"Split\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 1999.4073486328125,\n           
    \"y\": 1567.4814453125,\n            \"width\": 121.3331298828125,\n           
    \"height\": 149.333251953125\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"a30abab27b1d48e68ec3c90fb4c236c7\"\n        },\n       
    {\n            \"m_Id\": \"0f97e65d10984dde9eaf08770311dbc7\"\n        },\n       
    {\n            \"m_Id\": \"d6a27e12cd20436fb5403677a3bd9c5d\"\n        },\n       
    {\n            \"m_Id\": \"bd545a81db974ca09133f6b563e0dc32\"\n        },\n       
    {\n            \"m_Id\": \"b27e8be40a784e9a8493fc1daa47c922\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"separate\"\n    ],\n    \"m_Precision\": 0,\n   
    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.RedirectNodeData\",\n   
    \"m_ObjectId\": \"d943aacc20ec41efb86380dab6d412bf\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Redirect Node\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 2242.592529296875,\n            \"y\": 625.3333740234375,\n           
    \"width\": 56.0,\n            \"height\": 23.851806640625\n        }\n    },\n   
    \"m_Slots\": [\n        {\n            \"m_Id\": \"abac69a168ad4460be7b0e686a840776\"\n       
    },\n        {\n            \"m_Id\": \"45b0227909644d69a7520029101b6292\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    2,\n    \"m_Type\": \"UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget\",\n   
    \"m_ObjectId\": \"d9e748a6c55643dda4e8b1b8c673a9f7\"\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.CustomInterpolatorNode\",\n   
    \"m_ObjectId\": \"dab32ad82d9d43c6ba29cee5785d299a\",\n    \"m_Group\": {\n       
    \"m_Id\": \"fbd0a71e79964ac5a31cbfdcbee936d6\"\n    },\n    \"m_Name\": \"WorldPos
    (Custom Interpolator)\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    1684.66650390625,\n            \"y\": 1176.0,\n            \"width\": 226.5927734375,\n           
    \"height\": 278.4443359375\n        }\n    },\n    \"m_Slots\": [\n        {\n           
    \"m_Id\": \"9ebba7b24f0843bbac3aa7d35ea04402\"\n        }\n    ],\n    \"synonyms\":
    [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    },\n    \"customBlockNodeName\": \"WorldPos\",\n    \"serializedType\":
    3\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SplitNode\",\n   
    \"m_ObjectId\": \"dafbc94f76b74c2092e3bf0e915df7ee\",\n    \"m_Group\": {\n       
    \"m_Id\": \"f8cbdbe051ca4a71ab01d9dcc968e79e\"\n    },\n    \"m_Name\": \"Split\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 23.333248138427736,\n           
    \"y\": 802.66650390625,\n            \"width\": 120.81480407714844,\n           
    \"height\": 149.85198974609376\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"9abf30583b144ac6b0d2fcefc76ffd57\"\n        },\n       
    {\n            \"m_Id\": \"bdf41e729e12496cbaa767bd9974b49b\"\n        },\n       
    {\n            \"m_Id\": \"d56c68dc47a04c59b27323ce6d604be4\"\n        },\n       
    {\n            \"m_Id\": \"61bf837a21d64bc2b6aa5c5f47ceaeb4\"\n        },\n       
    {\n            \"m_Id\": \"4da8698a7871442a9afd4984905d657a\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"separate\"\n    ],\n    \"m_Precision\": 0,\n   
    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"de720166574d4fecb89f5396b679756c\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\": \"e0de0a602ee24bfa926c07ca98b9c439\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"VertexDescription.Position\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"b2aaac1d2768467abd81d5b2cecdebfd\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.Position\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector2MaterialSlot\",\n    \"m_ObjectId\": \"e0ec5df97b7c4bea9e85606ead9eb6cd\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 2,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GroupData\",\n   
    \"m_ObjectId\": \"e11a09d5c60541f89ece3e930dc4c9d3\",\n    \"m_Title\": \"GetGrassData\",\n   
    \"m_Position\": {\n        \"x\": 703.2000732421875,\n        \"y\": -305.60003662109377\n   
    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"e2d4ae6af501445b977f333cd38e8cd8\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\": \"e36b851d54454c11b0f5622f3d358269\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.BaseColor\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"b24eb957f7b5417b801370ac1855dd49\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.BaseColor\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.LerpNode\",\n    \"m_ObjectId\": \"e3cc90bcb3a44aee959bf0f0ca32b8e7\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Lerp\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 1464.81494140625,\n           
    \"y\": 580.2222290039063,\n            \"width\": 132.2222900390625,\n           
    \"height\": 142.59259033203126\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"40888c60b301464496a24bfc71f18ed6\"\n        },\n       
    {\n            \"m_Id\": \"17ea50cf4c764e01a91d6ca1ed7a25a7\"\n        },\n       
    {\n            \"m_Id\": \"f394501e272242bc927fcebd844bb99a\"\n        },\n       
    {\n            \"m_Id\": \"fd1a377c0f614e5592761adc5512705b\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"mix\",\n        \"blend\",\n        \"linear
    interpolate\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": false,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"e576d49be7954eda9f15f2bd252c52a5\",\n    \"m_Id\": 3,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n    \"m_ObjectId\":
    \"e579c6e921594f36b989619a164c7812\",\n    \"m_Id\": 5,\n    \"m_DisplayName\":
    \"col\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"col\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n   
    \"m_ObjectId\": \"e6d367a506624bf3bea700626e0a9a06\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"WorldPos\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"WorldPos\",\n    \"m_StageCapability\": 1,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.CustomInterpolatorNode\",\n    \"m_ObjectId\": \"e98be26ceb8f465583ae0ec74fb7f5e2\",\n   
    \"m_Group\": {\n        \"m_Id\": \"3e1ac251b21e44f296d990278ea9918d\"\n    },\n   
    \"m_Name\": \"ColorPaint (Custom Interpolator)\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 750.0,\n            \"y\": 204.9999542236328,\n           
    \"width\": 230.99993896484376,\n            \"height\": 94.00007629394531\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"fe20343ec4454c528a63be3a32d9451d\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    false,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"customBlockNodeName\":
    \"ColorPaint\",\n    \"serializedType\": 3\n}\n\n{\n    \"m_SGVersion\": 3,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.ColorShaderProperty\",\n    \"m_ObjectId\":
    \"eb0cede3a5064e43bd1ca69ef480ec60\",\n    \"m_Guid\": {\n        \"m_GuidSerialized\":
    \"4340e27b-91c8-4ad4-8502-cab220b69f40\"\n    },\n    \"m_Name\": \"TopTint\",\n   
    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\": \"TopTint\",\n   
    \"m_DefaultReferenceName\": \"_TopTint\",\n    \"m_OverrideReferenceName\": \"\",\n   
    \"m_GeneratePropertyBlock\": false,\n    \"m_UseCustomSlotLabel\": false,\n   
    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n    \"m_Precision\":
    0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": {\n        \"r\": 0.0,\n       
    \"g\": 0.0,\n        \"b\": 0.0,\n        \"a\": 0.0\n    },\n    \"isMainColor\":
    false,\n    \"m_ColorMode\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"f394501e272242bc927fcebd844bb99a\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"T\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"T\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector2MaterialSlot\",\n    \"m_ObjectId\":
    \"f41b0c2e2b02488da19963d8cf1789d5\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"worldUV\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"worldUV\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\":
    0.0,\n        \"y\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.GroupData\",\n    \"m_ObjectId\": \"f7330befd4a1454598de51903e691fbc\",\n   
    \"m_Title\": \"Blending Texture\",\n    \"m_Position\": {\n        \"x\": -111.19999694824219,\n       
    \"y\": -88.79998779296875\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.GroupData\",\n    \"m_ObjectId\": \"f8cbdbe051ca4a71ab01d9dcc968e79e\",\n   
    \"m_Title\": \"Top and Bottom Blend\",\n    \"m_Position\": {\n        \"x\":
    -238.39999389648438,\n        \"y\": 572.7999877929688\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GroupData\",\n    \"m_ObjectId\":
    \"fbd0a71e79964ac5a31cbfdcbee936d6\",\n    \"m_Title\": \"World Position Y\",\n   
    \"m_Position\": {\n        \"x\": 1659.199951171875,\n        \"y\": 1116.0\n   
    }\n}\n\n{\n    \"m_SGVersion\": 1,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty\",\n   
    \"m_ObjectId\": \"fbede82cbe61420d87bb2a70bd1336f6\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"cf091e82-d810-452a-9290-6c8d6a24f776\"\n    },\n    \"m_Name\":
    \"BlendMult\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"BlendMult\",\n    \"m_DefaultReferenceName\": \"_BlendMult\",\n    \"m_OverrideReferenceName\":
    \"\",\n    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": 1.0,\n    \"m_FloatType\": 0,\n   
    \"m_RangeValues\": {\n        \"x\": 0.0,\n        \"y\": 1.0\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"fd1a377c0f614e5592761adc5512705b\",\n    \"m_Id\": 3,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n    \"m_ObjectId\":
    \"fe20343ec4454c528a63be3a32d9451d\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 2,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.CategoryData\",\n   
    \"m_ObjectId\": \"fed5074684984b96893bfa5fd7432a95\",\n    \"m_Name\": \"\",\n   
    \"m_ChildObjectList\": [\n        {\n            \"m_Id\": \"163bfa18e4ab4e75b93815703c941ea0\"\n       
    },\n        {\n            \"m_Id\": \"217ecc1b3f414f6298c540a27a883e11\"\n       
    },\n        {\n            \"m_Id\": \"eb0cede3a5064e43bd1ca69ef480ec60\"\n       
    },\n        {\n            \"m_Id\": \"a650976d00ac4bc78760002cadf39d1c\"\n       
    },\n        {\n            \"m_Id\": \"fbede82cbe61420d87bb2a70bd1336f6\"\n       
    },\n        {\n            \"m_Id\": \"bddc018d13634e6183f54d41b94c1abb\"\n       
    }\n    ]\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n   
    \"m_ObjectId\": \"ff91fbe7a569454d8ae18f958fb4bf11\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"VertexDescription.CustomInterpolator\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"cb699555ee87458b91ba73b3e48934bb\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.ColorPaint#3\"\n}\n\n"
  m_AssetMaybeChangedOnDisk: 1
  m_AssetMaybeDeleted: 0
--- !u!114 &16
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f62a84c0b148b0a4582bdd9f1a69e6d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: ' Hot Reload'
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: " Hot Reload\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 182
    width: 1242
    height: 804
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &17
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12015, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Game
    m_Image: {fileID: -6423792434712278376, guid: 0000000000000000d000000000000000,
      type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Game\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 709
    width: 1168
    height: 222
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SerializedViewNames: []
  m_SerializedViewValues: []
  m_PlayModeViewName: GameView
  m_ShowGizmos: 0
  m_TargetDisplay: 0
  m_ClearColor: {r: 0, g: 0, b: 0, a: 0}
  m_TargetSize: {x: 1080, y: 2400}
  m_TextureFilterMode: 0
  m_TextureHideFlags: 61
  m_RenderIMGUI: 1
  m_EnterPlayModeBehavior: 0
  m_UseMipMap: 0
  m_VSyncEnabled: 0
  m_Gizmos: 0
  m_Stats: 1
  m_SelectedSizes: 07000000000000000000000012000000000000000000000000000000000000000000000000000000
  m_ZoomArea:
    m_HRangeLocked: 0
    m_VRangeLocked: 0
    hZoomLockedByDefault: 0
    vZoomLockedByDefault: 0
    m_HBaseRangeMin: -540
    m_HBaseRangeMax: 540
    m_VBaseRangeMin: -1200
    m_VBaseRangeMax: 1200
    m_HAllowExceedBaseRangeMin: 1
    m_HAllowExceedBaseRangeMax: 1
    m_VAllowExceedBaseRangeMin: 1
    m_VAllowExceedBaseRangeMax: 1
    m_ScaleWithWindow: 0
    m_HSlider: 0
    m_VSlider: 0
    m_IgnoreScrollWheelUntilClicked: 0
    m_EnableMouseInput: 1
    m_EnableSliderZoomHorizontal: 0
    m_EnableSliderZoomVertical: 0
    m_UniformScale: 1
    m_UpDirection: 1
    m_DrawArea:
      serializedVersion: 2
      x: 0
      y: 21
      width: 1168
      height: 201
    m_Scale: {x: 0.08375, y: 0.08375}
    m_Translation: {x: 584, y: 100.5}
    m_MarginLeft: 0
    m_MarginRight: 0
    m_MarginTop: 0
    m_MarginBottom: 0
    m_LastShownAreaInsideMargins:
      serializedVersion: 2
      x: -6973.1343
      y: -1200
      width: 13946.269
      height: 2400
    m_MinimalGUI: 1
  m_defaultScale: 0.08375
  m_LastWindowPixelSize: {x: 1168, y: 222}
  m_ClearInEditMode: 1
  m_NoCameraWarning: 1
  m_LowResolutionForAspectRatios: 01000001000000000000
  m_XRRenderMode: 0
  m_RenderTexture: {fileID: 0}
  m_showToolbar: 1
--- !u!114 &18
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12013, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Scene
    m_Image: {fileID: 2593428753322112591, guid: 0000000000000000d000000000000000,
      type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Scene\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 24
    width: 1168
    height: 659
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData:
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 1
      id: Tool Settings
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":1,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 1
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-grid-and-snap-toolbar
      index: 2
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-141.0,"y":-24.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -141, y: -24}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-scene-view-toolbar
      index: 0
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 0
      id: unity-search-toolbar
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":-24.0,"y":0.0},"m_FloatingSnapCorner":1,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: -24, y: 0}
      snapCorner: 1
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--left
      displayed: 1
      id: unity-transform-toolbar
      index: 0
      contents: '{"m_Layout":2,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 2
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--left
      displayed: 1
      id: unity-component-tools
      index: 1
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 197}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 2
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: Orientation
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-125.5,"y":-24.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -125.5, y: -24}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Light Settings
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Camera
      index: 1
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Cloth Constraints
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Cloth Collisions
      index: 3
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Navmesh Display
      index: 4
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Agent Display
      index: 5
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Obstacle Display
      index: 6
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Occlusion Culling
      index: 4
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Physics Debugger
      index: 5
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Scene Visibility
      index: 6
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Particles
      index: 7
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap
      index: 11
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap Palette Helper
      index: 12
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 0
      id: Brush Attributes
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-scene-view-camera-mode-toolbar
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Terrain Tools
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Brush Masks
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--left
      displayed: 0
      id: Scene View/Lighting Visualization Colors
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--left
      displayed: 1
      id: Overlays/OverlayMenu
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/PBR Validation Settings
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: unity-spline-inspector
      index: 8
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: APV Overlay
      index: 9
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: AINavigationOverlay
      index: 10
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: Floating
      displayed: 1
      id: SceneView/CamerasOverlay
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":true,"m_FloatingSnapOffset":{"x":-254.0,"y":-105.60000610351563},"m_SnapOffsetDelta":{"x":0.0,"y":0.000030517578125},"m_FloatingSnapCorner":3,"m_Size":{"x":250.0,"y":184.0},"m_SizeOverridden":true}'
      floating: 1
      collapsed: 0
      snapOffset: {x: -254, y: -105.600006}
      snapOffsetDelta: {x: 0, y: 0.000030517578}
      snapCorner: 3
      layout: 4
      size: {x: 250, y: 184}
      sizeOverridden: 1
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/TrailRenderer
      index: 11
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: Hot Reload
      index: 12
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    m_ContainerData:
    - containerId: overlay-toolbar__top
      scrollOffset: 0
    - containerId: overlay-toolbar__left
      scrollOffset: 0
    - containerId: overlay-container--left
      scrollOffset: 0
    - containerId: overlay-container--right
      scrollOffset: 0
    - containerId: overlay-toolbar__right
      scrollOffset: 0
    - containerId: overlay-toolbar__bottom
      scrollOffset: 0
    - containerId: Floating
      scrollOffset: 0
    m_OverlaysVisible: 1
  m_WindowGUID: 7883d1f07e30612469d59cc24efb40ee
  m_Gizmos: 1
  m_OverrideSceneCullingMask: 6917529027641081856
  m_SceneIsLit: 1
  m_SceneLighting: 1
  m_2DMode: 0
  m_isRotationLocked: 0
  m_PlayAudio: 0
  m_AudioPlay: 0
  m_DebugDrawModesUseInteractiveLightBakingData: 0
  m_Position:
    m_Target: {x: 0, y: 0.5, z: 0}
    speed: 2
    m_Value: {x: 0, y: 0.5, z: 0}
  m_RenderMode: 0
  m_CameraMode:
    drawMode: 0
    name: Shaded
    section: Shading Mode
  m_ValidateTrueMetals: 0
  m_DoValidateTrueMetals: 0
  m_SceneViewState:
    m_AlwaysRefresh: 0
    showFog: 1
    showSkybox: 1
    showFlares: 1
    showImageEffects: 1
    showParticleSystems: 1
    showVisualEffectGraphs: 1
    m_FxEnabled: 1
  m_Grid:
    xGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    yGrid:
      m_Fade:
        m_Target: 1
        speed: 2
        m_Value: 1
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    zGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    m_ShowGrid: 1
    m_GridAxis: 1
    m_gridOpacity: 0.5
  m_Rotation:
    m_Target: {x: -0.36011708, y: -0.009244398, z: 0.0035639398, w: -0.9328936}
    speed: 2
    m_Value: {x: -0.36011708, y: -0.009244398, z: 0.0035639396, w: -0.9328935}
  m_Size:
    m_Target: 21.917356
    speed: 2
    m_Value: 21.917356
  m_Ortho:
    m_Target: 1
    speed: 2
    m_Value: 1
  m_CameraSettings:
    m_Speed: 1
    m_SpeedNormalized: 0.5
    m_SpeedMin: 0.01
    m_SpeedMax: 2
    m_EasingEnabled: 1
    m_EasingDuration: 0.4
    m_AccelerationEnabled: 1
    m_FieldOfViewHorizontalOrVertical: 60
    m_NearClip: 0.03
    m_FarClip: 10000
    m_DynamicClip: 1
    m_OcclusionCulling: 0
  m_LastSceneViewRotation: {x: 0.0041139154, y: 0.589632, z: 0.0030174418, w: -0.8076856}
  m_LastSceneViewOrtho: 1
  m_Viewpoint:
    m_SceneView: {fileID: 18}
    m_CameraOverscanSettings:
      m_Opacity: 50
      m_Scale: 1
  m_ReplacementShader: {fileID: 0}
  m_ReplacementString: 
  m_SceneVisActive: 1
  m_LastLockedObject: {fileID: 0}
  m_LastDebugDrawMode:
    drawMode: 35
    name: 
    section: 
  m_ViewIsLockedToObject: 0
--- !u!114 &19
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12914, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Animator
    m_Image: {fileID: -1673928668082335149, guid: 0000000000000000d000000000000000,
      type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Animator\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 79.200005
    width: 761.4
    height: 609.2
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_ViewTransforms:
    m_KeySerializationHelper:
    - {fileID: 4340677975400109351, guid: 2b79bfcd10c883d4f8763128103ee199, type: 2}
    m_ValueSerializationHelper:
    - e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  m_PreviewAnimator: {fileID: 0}
  m_AnimatorController: {fileID: 9100000, guid: 2b79bfcd10c883d4f8763128103ee199,
    type: 2}
  m_BreadCrumbs:
  - m_Target: {fileID: 4340677975400109351, guid: 2b79bfcd10c883d4f8763128103ee199,
      type: 2}
    m_ScrollPosition: {x: 0, y: 0}
  stateMachineGraph: {fileID: 0}
  stateMachineGraphGUI: {fileID: 0}
  blendTreeGraph: {fileID: 0}
  blendTreeGraphGUI: {fileID: 0}
  m_AutoLiveLink: 1
  m_MiniTool: 0
  m_LockTracker:
    m_IsLocked: 0
  m_CurrentEditor: 0
  m_LayerEditor:
    m_SelectedLayerIndex: 0
--- !u!114 &20
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12070, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 900, y: 216}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Profiler
    m_Image: {fileID: -1089619856830078684, guid: 0000000000000000d000000000000000,
      type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Profiler\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 73
    width: 1510
    height: 477
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_Recording: 1
  m_ActiveNativePlatformSupportModuleName: 
  m_AllModules:
  - rid: 3411904549776785408
  - rid: 3411904549776785409
  - rid: 3411904549776785410
  - rid: 3411904549776785411
  - rid: 3411904549776785412
  - rid: 3411904549776785413
  - rid: 3411904549776785414
  - rid: 3411904549776785415
  - rid: 3411904549776785418
  - rid: 3411904549776785419
  - rid: 3411904549776785420
  - rid: 3411904549776785421
  - rid: 644553861171511365
  - rid: 3411904549776785422
  - rid: 3411904549776785423
  m_CallstackRecordMode: 1
  m_ClearOnPlay: 0
  references:
    version: 2
    RefIds:
    - rid: 644553861171511365
      type: {class: AddressablesProfilerModule, ns: UnityEditor.AddressableAssets.Diagnostics,
        asm: Unity.Addressables.Editor}
      data:
        m_Identifier: UnityEditor.AddressableAssets.Diagnostics.AddressablesProfilerModule,
          Unity.Addressables.Editor, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    - rid: 3411904549776785408
      type: {class: CPUProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.CPUProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewType: 0
        updateViewLive: 0
        m_CurrentFrameIndex: 1917
        m_HierarchyOverruledThreadFromSelection: 0
        m_ProfilerViewFilteringOptions: 1
        m_FrameDataHierarchyView:
          m_Serialized: 1
          m_TreeViewState:
            scrollPos: {x: 0, y: 0}
            m_SelectedIDs: 
            m_LastClickedID: 0
            m_ExpandedIDs: 
            m_RenameOverlay:
              m_UserAcceptedRename: 0
              m_Name: 
              m_OriginalName: 
              m_EditFieldRect:
                serializedVersion: 2
                x: 0
                y: 0
                width: 0
                height: 0
              m_UserData: 0
              m_IsWaitingForDelay: 0
              m_IsRenaming: 0
              m_OriginalEventType: 11
              m_IsRenamingFilename: 0
              m_TrimLeadingAndTrailingWhitespace: 0
              m_ClientGUIView: {fileID: 0}
            m_SearchString: 
          m_MultiColumnHeaderState:
            m_Columns:
            - width: 1202
              sortedAscending: 1
              headerContent:
                m_Text: Overview
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: 
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 200
              maxWidth: 1000000
              autoResize: 1
              allowToggleVisibility: 0
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Total
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: 
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Self
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: 
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Calls
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: 
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: GC Alloc
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: 
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Time ms
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: 
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Self ms
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: 
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 25
              sortedAscending: 0
              headerContent:
                m_Text: 
                m_Image: {fileID: -5161429177145976760, guid: 0000000000000000d000000000000000,
                  type: 0}
                m_Tooltip: Warnings
                m_TextWithWhitespace: 
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 25
              maxWidth: 25
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            m_VisibleColumns: 0000000001000000020000000300000004000000050000000600000007000000
            m_SortedColumns: 05000000
          m_ThreadIndexInThreadNames: 0
          m_DetailedViewType: 0
          m_DetailedViewSpliterState:
            ID: 0
            splitterInitialOffset: 0
            currentActiveSplitter: -1
            realSizes:
            - 0
            - 0
            relativeSizes:
            - 0.7
            - 0.3
            minSizes:
            - 450
            - 50
            maxSizes:
            - 0
            - 0
            lastTotalSize: 0
            splitSize: 6
            xOffset: 0
            m_Version: 1
            oldRealSizes: 
            oldMinSizes: 
            oldMaxSizes: 
            oldSplitSize: 0
          m_DetailedObjectsView:
            m_SelectedID: -1
            m_TreeViewState:
              scrollPos: {x: 0, y: 0}
              m_SelectedIDs: 
              m_LastClickedID: 0
              m_ExpandedIDs: 
              m_RenameOverlay:
                m_UserAcceptedRename: 0
                m_Name: 
                m_OriginalName: 
                m_EditFieldRect:
                  serializedVersion: 2
                  x: 0
                  y: 0
                  width: 0
                  height: 0
                m_UserData: 0
                m_IsWaitingForDelay: 0
                m_IsRenaming: 0
                m_OriginalEventType: 11
                m_IsRenamingFilename: 0
                m_TrimLeadingAndTrailingWhitespace: 0
                m_ClientGUIView: {fileID: 0}
              m_SearchString: 
            m_MultiColumnHeaderState:
              m_Columns: []
              m_VisibleColumns: 
              m_SortedColumns: 
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
          m_DetailedCallsView:
            m_SelectedID: -1
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
            m_CalleesTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
            m_CallersTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
          m_FullThreadName: Main Thread
          m_ThreadName: Main Thread
          <threadId>k__BackingField: 25988
          <threadIndex>k__BackingField: 0
          m_GroupName: 
    - rid: 3411904549776785409
      type: {class: GPUProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.GPUProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewType: 0
        updateViewLive: 0
        m_CurrentFrameIndex: -1
        m_HierarchyOverruledThreadFromSelection: 0
        m_ProfilerViewFilteringOptions: 1
        m_FrameDataHierarchyView:
          m_Serialized: 0
          m_TreeViewState:
            scrollPos: {x: 0, y: 0}
            m_SelectedIDs: 
            m_LastClickedID: 0
            m_ExpandedIDs: 
            m_RenameOverlay:
              m_UserAcceptedRename: 0
              m_Name: 
              m_OriginalName: 
              m_EditFieldRect:
                serializedVersion: 2
                x: 0
                y: 0
                width: 0
                height: 0
              m_UserData: 0
              m_IsWaitingForDelay: 0
              m_IsRenaming: 0
              m_OriginalEventType: 11
              m_IsRenamingFilename: 0
              m_TrimLeadingAndTrailingWhitespace: 0
              m_ClientGUIView: {fileID: 0}
            m_SearchString: 
          m_MultiColumnHeaderState:
            m_Columns: []
            m_VisibleColumns: 
            m_SortedColumns: 
          m_ThreadIndexInThreadNames: 0
          m_DetailedViewType: 0
          m_DetailedViewSpliterState:
            ID: 0
            splitterInitialOffset: 0
            currentActiveSplitter: 0
            realSizes: []
            relativeSizes: []
            minSizes: []
            maxSizes: []
            lastTotalSize: 0
            splitSize: 0
            xOffset: 0
            m_Version: 1
            oldRealSizes: 
            oldMinSizes: 
            oldMaxSizes: 
            oldSplitSize: 0
          m_DetailedObjectsView:
            m_SelectedID: 0
            m_TreeViewState:
              scrollPos: {x: 0, y: 0}
              m_SelectedIDs: 
              m_LastClickedID: 0
              m_ExpandedIDs: 
              m_RenameOverlay:
                m_UserAcceptedRename: 0
                m_Name: 
                m_OriginalName: 
                m_EditFieldRect:
                  serializedVersion: 2
                  x: 0
                  y: 0
                  width: 0
                  height: 0
                m_UserData: 0
                m_IsWaitingForDelay: 0
                m_IsRenaming: 0
                m_OriginalEventType: 11
                m_IsRenamingFilename: 0
                m_TrimLeadingAndTrailingWhitespace: 0
                m_ClientGUIView: {fileID: 0}
              m_SearchString: 
            m_MultiColumnHeaderState:
              m_Columns: []
              m_VisibleColumns: 
              m_SortedColumns: 
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
          m_DetailedCallsView:
            m_SelectedID: -1
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
            m_CalleesTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
            m_CallersTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
          m_FullThreadName: Main Thread
          m_ThreadName: Main Thread
          <threadId>k__BackingField: 0
          <threadIndex>k__BackingField: -1
          m_GroupName: 
    - rid: 3411904549776785410
      type: {class: RenderingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.RenderingProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 3411904549776785411
      type: {class: MemoryProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.MemoryProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewSplit:
          ID: 0
          splitterInitialOffset: 0
          currentActiveSplitter: -1
          realSizes:
          - 0
          - 0
          relativeSizes:
          - 0.7
          - 0.3
          minSizes:
          - 450
          - 50
          maxSizes:
          - 0
          - 0
          lastTotalSize: 0
          splitSize: 6
          xOffset: 0
          m_Version: 1
          oldRealSizes: 
          oldMinSizes: 
          oldMaxSizes: 
          oldSplitSize: 0
    - rid: 3411904549776785412
      type: {class: AudioProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.AudioProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ShowInactiveDSPChains: 0
        m_HighlightAudibleDSPChains: 1
        m_DSPGraphZoomFactor: 1
        m_DSPGraphHorizontalLayout: 0
    - rid: 3411904549776785413
      type: {class: VideoProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.VideoProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 3411904549776785414
      type: {class: PhysicsProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.PhysicsProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 3411904549776785415
      type: {class: Physics2DProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.Physics2DProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 3411904549776785418
      type: {class: UIProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.UIProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 3411904549776785419
      type: {class: UIDetailsProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.UIDetailsProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 3411904549776785420
      type: {class: GlobalIlluminationProfilerModule, ns: UnityEditorInternal.Profiling,
        asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.GlobalIlluminationProfilerModule,
          UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 3411904549776785421
      type: {class: VirtualTexturingProfilerModule, ns: UnityEditorInternal.Profiling,
        asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.VirtualTexturingProfilerModule,
          UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_VTProfilerView:
          rid: 3411904549776785424
    - rid: 3411904549776785422
      type: {class: FileIOProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.FileIOProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 3411904549776785423
      type: {class: AssetLoadingProfilerModule, ns: UnityEditorInternal.Profiling,
        asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.AssetLoadingProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 3411904549776785424
      type: {class: VirtualTexturingProfilerView, ns: UnityEditor, asm: UnityEditor.CoreModule}
      data:
        m_SortAscending: 0
        m_SortedColumn: -1
--- !u!114 &21
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e98b4a8982e76bc4d86c10db25cd7193, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 430, y: 250}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Addressables Groups
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "UnityEditor.AddressableAssets.GUI.AddressableAssetsWindow\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 182
    width: 1242
    height: 804
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_GroupEditor:
    m_TreeState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: be0e2ae2
      m_LastClickedID: -500560194
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_TrimLeadingAndTrailingWhitespace: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
      columnWidths: []
      sortOrder: []
      sortOrderList:
      - 5c8467acad44f2946a990699ab35e857
      - ee0d33ca9ffe29f42887f74ff9aad34d
      - 7a746259aa7feb44fab860a66ae38b67
      - c3f2d660f2f5f294d8487abd58767cf2
      - b4ac553bc4c92084abd30cc1db99568e
      - 9b7797571d0f0584eae3a5f7eca85ccc
      - 88216c3647ac3e345a2fb1891688aac3
      - ff30156afffb6774fb6cac32dd6bd8f1
      - 67cd48f8a792e0a4cbf223f49ab583bc
      - 8980327ad9947b543aed67c47e1dd167
      - 40e0f3889066146489050b25b13e9f4c
      - 8fca7507d5a2b4444adc8d5624640427
      - 2b2242bdf8be349419d1aae28267df25
      - 9ff0fc13a1a71be458150d0fc7a5329c
      - 3dd7b7b9268929a40853bcfdb9ff2f4a
      - 5c9502131cf01094fb9199c3f953501f
      - 71fd955c31f4d5b41850f56f0ffda5fa
      - 99ee641edab8589428a3e59b8fb9b9a5
      - 2d5c93e8570cca34cbc06c15ad6e4147
      - a6b4bcf29b79f4e4b883fab1fd2af1e9
      - 481e7f96c2850eb409952eae94308c0a
      - 722a2bb975205414286b97bfaf82aba3
      - 8b50acd4e91ed77498da1c7b9cc1b8e4
      - 410b2d5610c7d3f4ba3c62c73199de83
      - 8c9165843208ef6438c7a8def6b008e1
      - beecb685d8459c043b518e483f8aff8f
      - b3a0d9844c4a9a645998a83032110afa
      - 78eddc33fbd8650469424c6e88d74e65
      - 9b50ef0524ee06d47a7e59a69df6a13e
      - 1fe7774b07798334f87a5b3c0767ac1b
      - 629f706155df38144b14e8c6bd7fbbe3
      - e498c98afd1a8c348b83ae1f93b0b3c8
      - 6a7d0f5c0ede2bb4fb484525135d8c61
      - 578c35583b253dc4f90d0025eba9864f
      - b9fbc4e24d40cdb43a6d3a46c3b65892
      - 619779e60ce959f4ea631882bf5d8e03
      - 88e26206201bbe448b57226c78c0ccd7
      - 4aed60e317f0f3248a7498078670b6e0
      - 26973c04086267044b56629948d34913
      - d9a2372f467fe5b4a90cfd2ee0f237eb
      - 1c2d613f411eae548bdcbd5e606ce624
      - 0bb86f8ab537624498f6d10c0fa5fb17
      - c06d46dd650fdc04d982b1206c59252d
      - d3f1c62fac36a094eb2acae7dc29de08
      - b22ccafb9d043b7409da0e571b6162c1
      - ba7054f15e9bf2c478753d6f090d1662
      - 948d1a21d52a442499aaf387623bda39
      - 9d806431ecba9b647a5abfc1c45a8d25
      - 150a209bec291f543909c2072ecd92f8
      - b840cf8bbb2c20a43b45a6a364f50818
      - 6d228aa13d0a0844a8ffac550659db6e
      - cf9fe39b992d75a4e909b41cf1093ace
      - aae4fe28ac1a34e4e81c69e282bec70a
      - 9fe7cc9af0b7c1b41b13d2037f01d5bd
      - 946d75468834d944aa1222eb76d4b336
      - 6270530ca53c4334aa1c25317ed4c91e
      - 0d39c88d3813b5d4db69d994dec343d9
      - 8285a3e3b2621b74cb10df644fcf299f
      - 047bd0c7d10f4934c8404f16ccdd26e6
      - e63f0a9ff20a6c243979ce23a55bdacc
      - 3cfe1b593a9956543a2364cdce2d6bc5
      - 0f149bdad7293a343b4f8d56933cade8
      - d4893531ba55334499db5c0707ac5ac7
      - 18ed08fa3bb392545bddedd47413f125
      - d1833afcc0452fa458be19ca3c8ae76e
      - cd99ebcd22d3c8e4ebba0d606f21ae03
      - afc7dec5a8281b44ebbd96f043d00251
      - 0b805e600d3db9b439cf34d10dae2c76
      - ecb9f05084315f042afb82177755d7cb
      - 54b20e4871b09d54ca9308d119474df4
      - 970b5f5a4f3a02741ba483c84f2a9783
      - 5fefbf8097fb3d2479ade10f98f3fefe
      - 02809b8014ccbf444adf9aca1640eb89
      - ca23a5e9185e69846824246e83003fa5
      - 3915481d174d82c4daa0a9d9d465fee6
      - 90896dd737c33c6459362336668e6f0c
      - 684b80367d35b1d48b2a3ae105f63c74
      - 701cec9e8dbe1c04183059a9ba7af8c8
      - eec10a58337d17c4ab6791d5a2b44633
    m_Mchs:
      m_Columns:
      - width: 25
        sortedAscending: 0
        headerContent:
          m_Text: 
          m_Image: {fileID: 0}
          m_Tooltip: Notifications
          m_TextWithWhitespace: 
        contextMenuText: 
        headerTextAlignment: 0
        sortingArrowAlignment: 1
        minWidth: 25
        maxWidth: 25
        autoResize: 1
        allowToggleVisibility: 1
        canSort: 0
        userData: 0
      - width: 223.42189
        sortedAscending: 0
        headerContent:
          m_Text: Group Name \ Addressable Name
          m_Image: {fileID: 0}
          m_Tooltip: Address used to load asset at runtime
          m_TextWithWhitespace: "Group Name \\ Addressable Name\u200B"
        contextMenuText: 
        headerTextAlignment: 0
        sortingArrowAlignment: 1
        minWidth: 100
        maxWidth: 10000
        autoResize: 1
        allowToggleVisibility: 1
        canSort: 1
        userData: 0
      - width: 20
        sortedAscending: 0
        headerContent:
          m_Text: 
          m_Image: {fileID: 8477472853372924405, guid: 0000000000000000d000000000000000,
            type: 0}
          m_Tooltip: Asset type
          m_TextWithWhitespace: 
        contextMenuText: 
        headerTextAlignment: 0
        sortingArrowAlignment: 1
        minWidth: 20
        maxWidth: 20
        autoResize: 1
        allowToggleVisibility: 1
        canSort: 0
        userData: 0
      - width: 223.42189
        sortedAscending: 0
        headerContent:
          m_Text: Path
          m_Image: {fileID: 0}
          m_Tooltip: Current Path of asset
          m_TextWithWhitespace: "Path\u200B"
        contextMenuText: 
        headerTextAlignment: 0
        sortingArrowAlignment: 1
        minWidth: 100
        maxWidth: 10000
        autoResize: 1
        allowToggleVisibility: 1
        canSort: 1
        userData: 0
      - width: 67.261734
        sortedAscending: 0
        headerContent:
          m_Text: Labels
          m_Image: {fileID: 0}
          m_Tooltip: Assets can have multiple labels
          m_TextWithWhitespace: "Labels\u200B"
        contextMenuText: 
        headerTextAlignment: 0
        sortingArrowAlignment: 1
        minWidth: 20
        maxWidth: 1000
        autoResize: 1
        allowToggleVisibility: 1
        canSort: 1
        userData: 0
      m_VisibleColumns: 0000000001000000020000000300000004000000
      m_SortedColumns: 01000000
    window: {fileID: 21}
    m_VerticalSplitterPercent: 0.8
--- !u!114 &22
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4ddf1d9ac412b374eb1b09a55a68f96d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: "\u6253\u5305\u5DE5\u5177 (BuildTool)"
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "\u6253\u5305\u5DE5\u5177 (BuildTool)\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 129
    width: 1168
    height: 857
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: inspectorTargetSerialized
      Entry: 6
      Data: 
  labelWidth: 0.33
  windowPadding: {x: 4, y: 4, z: 4, w: 4}
  useScrollView: 1
  drawUnityEditorPreview: 0
  wrappedAreaMaxHeight: 1000
  sdkConfig: {fileID: 11400000, guid: 47af255fbf980564ebf255c9f3fbc67a, type: 2}
  keyStorePath: /../../../_Out/om001.keystore
  IsDebug: 0
  AppVersion: 1.0
  IsBuildAppBundle: 0
  IsBuildTable: 1
  IsBuildAB: 1
--- !u!114 &23
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12061, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Hierarchy
    m_Image: {fileID: 7966133145522015247, guid: 0000000000000000d000000000000000,
      type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Hierarchy\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1
    y: 24
    width: 372
    height: 466
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SceneHierarchy:
    m_TreeViewState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 5cbcfeff74bcfeff6ac4feff20c5feff6a1effff5658ffffb259ffff3abbffff88cdffff82ddffff14fbffff5aff00005eff0000
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_TrimLeadingAndTrailingWhitespace: 0
        m_ClientGUIView: {fileID: 11}
      m_SearchString: 
    m_ExpandedScenes: []
    m_CurrenRootInstanceID: 0
    m_LockTracker:
      m_IsLocked: 0
    m_CurrentSortingName: TransformSorting
  m_WindowGUID: b87483a07291ecb4b8bae557d8545f10
--- !u!114 &24
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12003, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Console
    m_Image: {fileID: -4327648978806127646, guid: 0000000000000000d000000000000000,
      type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Console\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1
    y: 516
    width: 372
    height: 415
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &25
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12014, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 230, y: 250}
  m_MaxSize: {x: 10000, y: 10000}
  m_TitleContent:
    m_Text: Project
    m_Image: {fileID: -5467254957812901981, guid: 0000000000000000d000000000000000,
      type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Project\u200B"
  m_Pos:
    serializedVersion: 2
    x: 375
    y: 24
    width: 393
    height: 907
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SearchFilter:
    m_NameFilter: 
    m_ClassNames: []
    m_AssetLabels: []
    m_AssetBundleNames: []
    m_ReferencingInstanceIDs: 
    m_SceneHandles: 
    m_ShowAllHits: 0
    m_SkipHidden: 0
    m_SearchArea: 1
    m_Folders: []
    m_Globs: []
    m_ProductIds: 
    m_AnyWithAssetOrigin: 0
    m_OriginalText: 
    m_ImportLogFlags: 0
    m_FilterByTypeIntersection: 0
  m_ViewMode: 0
  m_StartGridSize: 64
  m_LastFolders: []
  m_LastFoldersGridSize: -1
  m_LastProjectPath: D:\Study\OM001\UnityProject\OM001
  m_LockTracker:
    m_IsLocked: 0
  m_FolderTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: e6010100
    m_LastClickedID: 66022
    m_ExpandedIDs: 0000000094d7000096d7000098d700009ad700009cd700009ed70000a0d70000a2d70000a4d70000a6d70000a8d70000aad70000acd70000aed70000b0d70000b2d70000b4d70000b6d70000b8d70000bad70000bcd70000bed70000c0d70000c2d70000c4d70000c6d70000c8d70000cad70000ccd70000ced70000d0d70000d2d70000d4d70000d6d70000d8d70000dad70000dcd70000ded70000e0d70000e2d70000e4d70000e6d70000e8d70000ead70000ecd70000eed70000f0d70000f2d70000f4d70000f6d70000f8d70000fad70000fcd70000fed7000000d8000002d8000004d8000006d8000008d800000ad800000cd800000ed8000010d8000012d8000014d8000016d8000018d800001ad800001cd800001ed8000020d8000022d8000024d8000026d8000028d800002ad800002cd800002ed8000030d8000032d8000034d8000036d8000038d800003ad800003cd800003ed8000040d8000042d8000044d8000046d8000048d800004ad800004cd800004ed8000050d8000052d8000054d8000056d8000058d800005ad800005cd800005ed8000060d8000062d8000064d8000066d8000068d800006ad800006cd800006ed8000070d8000072d8000074d8000076d8000078d800007ad800007cd800007ed8000080d8000082d8000084d8000086d8000088d800008ad800008cd800008ed8000090d8000092d8000094d8000096d8000098d800009ad800009cd800009ed80000a0d80000a2d80000a4d80000a6d80000a8d80000aad80000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_AssetTreeState:
    scrollPos: {x: 0, y: 67}
    m_SelectedIDs: 
    m_LastClickedID: 0
    m_ExpandedIDs: 0000000094d7000096d7000098d700009ad700009cd700009ed70000a0d70000a2d70000a4d70000a6d70000a8d70000aad70000acd70000aed70000b0d70000b2d70000b4d70000b6d70000b8d70000bad70000bcd70000bed70000c0d70000c2d70000c4d70000c6d70000c8d70000cad70000ccd70000ced70000d0d70000d2d70000d4d70000d6d70000d8d70000dad70000dcd70000ded70000e0d70000e2d70000e4d70000e6d70000e8d70000ead70000ecd70000eed70000f0d70000f2d70000f4d70000f6d70000f8d70000fad70000fcd70000fed7000000d8000002d8000004d8000006d8000008d800000ad800000cd800000ed8000010d8000012d8000014d8000016d8000018d800001ad800001cd800001ed8000020d8000022d8000024d8000026d8000028d800002ad800002cd800002ed8000030d8000032d8000034d8000036d8000038d800003ad800003cd800003ed8000040d8000042d8000044d8000046d8000048d800004ad800004cd800004ed8000050d8000052d8000054d8000056d8000058d800005ad800005cd800005ed8000060d8000062d8000064d8000066d8000068d800006ad800006cd800006ed8000070d8000072d8000074d8000076d8000078d800007ad800007cd800007ed8000080d8000082d8000084d8000086d8000088d800008ad800008cd800008ed8000090d8000092d8000094d8000096d8000098d800009ad800009cd800009ed80000a0d80000a2d80000a4d80000a6d80000a8d80000aad80000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 13}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_ListAreaState:
    m_SelectedInstanceIDs: 
    m_LastClickedInstanceID: 0
    m_HadKeyboardFocusLastEvent: 1
    m_ExpandedInstanceIDs: 
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
    m_NewAssetIndexInList: -1
    m_ScrollPosition: {x: 0, y: 0}
    m_GridSize: 64
  m_SkipHiddenPackages: 0
  m_DirectoriesAreaWidth: 115
--- !u!114 &26
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12019, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 275, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Inspector
    m_Image: {fileID: -2667387946076563598, guid: 0000000000000000d000000000000000,
      type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Inspector\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1939
    y: 24
    width: 621
    height: 907
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_ObjectsLockedBeforeSerialization: []
  m_InstanceIDsLockedBeforeSerialization: 
  m_PreviewResizer:
    m_CachedPref: 447
    m_ControlHash: -371814159
    m_PrefName: Preview_InspectorPreview
  m_LastInspectedObjectInstanceID: -1
  m_LastVerticalScrollValue: 0
  m_GlobalObjectId: 
  m_InspectorMode: 0
  m_LockTracker:
    m_IsLocked: 0
  m_PreviewWindow: {fileID: 0}
--- !u!114 &27
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12079, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 390, y: 390}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Lighting
    m_Image: {fileID: -1347227620855488341, guid: 0000000000000000d000000000000000,
      type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Lighting\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1983
    y: 79
    width: 576
    height: 907
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
